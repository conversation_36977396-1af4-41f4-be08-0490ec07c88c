diff --git a/diff_output.txt b/diff_output.txt
index 489fa69..e69de29 100644
--- a/diff_output.txt
+++ b/diff_output.txt
@@ -1,54 +0,0 @@
-diff --git a/src/features/timestampNote.ts b/src/features/timestampNote.ts
-index a1d07bd..09012d4 100644
---- a/src/features/timestampNote.ts
-+++ b/src/features/timestampNote.ts
-@@ -23,18 +23,28 @@ function generateTimestampFileName(): string {
- // 主要功能函数
- export async function mergeToTimestampNote(options: TimestampNoteOptions): Promise<void> {
- 	const { editor, app, selectedContent } = options;
--	
-+
- 	try {
- 		// 生成时间戳文件名
- 		const timestamp = generateTimestampFileName();
- 		const fileName = `${timestamp}.md`;
--		
--		// 在根目录查找是否已存在今天的时间戳笔记
--		const todayPrefix = timestamp.substring(0, 12); // YYYYMMDD
-+
-+		// 目标目录路径（相对于 vault 根目录）
-+		const targetDirPath = '03-仓库/01-笔记/01-聚焦';
-+
-+		// 确保目标目录存在
-+		const targetDir = app.vault.getAbstractFileByPath(targetDirPath);
-+		if (!targetDir) {
-+			// 如果目录不存在，创建目录结构
-+			await app.vault.createFolder(targetDirPath);
-+		}
-+
-+		// 在目标目录查找是否已存在今天的时间戳笔记
-+		const todayPrefix = timestamp.substring(0, 8); // YYYYMMDD (只取日期部分)
- 		const existingFiles = app.vault.getMarkdownFiles();
--		const existingFile = existingFiles.find(file => 
--			file.basename.startsWith(todayPrefix) && 
--			file.parent?.path === '' // 确保是在根目录
-+		const existingFile = existingFiles.find(file =>
-+			file.basename.startsWith(todayPrefix) &&
-+			file.parent?.path === targetDirPath // 确保是在目标目录
- 		);
- 
- 		let targetFile: TFile;
-@@ -54,9 +64,10 @@ export async function mergeToTimestampNote(options: TimestampNoteOptions): Promi
- 				new Notice(`✅ 内容已追加到现有笔记: ${existingFile.basename}`);
- 			}
- 		} else {
--			// 如果不存在，创建新的时间戳笔记
--			targetFile = await app.vault.create(fileName, content);
--			
-+			// 如果不存在，创建新的时间戳笔记（在目标目录中）
-+			const fullFilePath = `${targetDirPath}/${fileName}`;
-+			targetFile = await app.vault.create(fullFilePath, content);
-+
- 			// 根据是否有内容调整提示信息
- 			if (selectedContent.trim() === '') {
- 				new Notice(`✅ 已创建时间戳笔记并链接: ${fileName}`);
diff --git a/src/features/timestampNote.ts b/src/features/timestampNote.ts
index 09012d4..c092c82 100644
--- a/src/features/timestampNote.ts
+++ b/src/features/timestampNote.ts
@@ -39,41 +39,16 @@ export async function mergeToTimestampNote(options: TimestampNoteOptions): Promi
 			await app.vault.createFolder(targetDirPath);
 		}
 
-		// 在目标目录查找是否已存在今天的时间戳笔记
-		const todayPrefix = timestamp.substring(0, 8); // YYYYMMDD (只取日期部分)
-		const existingFiles = app.vault.getMarkdownFiles();
-		const existingFile = existingFiles.find(file =>
-			file.basename.startsWith(todayPrefix) &&
-			file.parent?.path === targetDirPath // 确保是在目标目录
-		);
+		// 创建新的时间戳笔记（每个时间戳都是唯一的）
+		const fullFilePath = `${targetDirPath}/${fileName}`;
+		const content = selectedContent.trim();
+		const targetFile = await app.vault.create(fullFilePath, content);
 
-		let targetFile: TFile;
-		let content = selectedContent.trim();
-
-		if (existingFile) {
-			// 如果存在今天的时间戳笔记，追加内容
-			const existingContent = await app.vault.read(existingFile);
-			const newContent = `${existingContent}\n\n---\n\n${content}`;
-			await app.vault.modify(existingFile, newContent);
-			targetFile = existingFile;
-			
-			// 根据是否有内容调整提示信息
-			if (selectedContent.trim() === '') {
-				new Notice(`✅ 已链接到时间戳笔记: ${existingFile.basename}`);
-			} else {
-				new Notice(`✅ 内容已追加到现有笔记: ${existingFile.basename}`);
-			}
+		// 根据是否有内容调整提示信息
+		if (selectedContent.trim() === '') {
+			new Notice(`✅ 已创建时间戳笔记并链接: ${fileName}`);
 		} else {
-			// 如果不存在，创建新的时间戳笔记（在目标目录中）
-			const fullFilePath = `${targetDirPath}/${fileName}`;
-			targetFile = await app.vault.create(fullFilePath, content);
-
-			// 根据是否有内容调整提示信息
-			if (selectedContent.trim() === '') {
-				new Notice(`✅ 已创建时间戳笔记并链接: ${fileName}`);
-			} else {
-				new Notice(`✅ 已创建新的时间戳笔记: ${fileName}`);
-			}
+			new Notice(`✅ 已创建新的时间戳笔记: ${fileName}`);
 		}
 
 		// 在原编辑区域替换为链接
