import { <PERSON><PERSON>, Editor, <PERSON><PERSON><PERSON>iew, Notice, <PERSON>lug<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, ItemView } from 'obsidian';
import { CanvasExporter } from './canvas-export';

// 导入类型定义
import { MyPluginSettings, ClassificationResponse } from './types';

// 导入设置相关
import { DEFAULT_SETTINGS, SummarySettingTab } from './settings';

// 导入重构后的功能函数
import { copySummaryLink } from './features/summaryUtils';
import { moveToArchive } from './features/archiveNote';
import { handleImageLinks } from './features/imageLinkConverter';
import { processNoteLinks } from './features/knowledgeDocumentExport';
import { handleNoteClassification } from './features/noteClassification';
import { handleKnowledgeStructure } from './features/knowledgeTreeGeneration';
import { insertTimeBlock } from './features/timeBlockManager';
import { generateAbstractDirectory } from './features/abstractDirectory';
import { handleCreateNoteOutline } from './features/noteOutlineCreation';
import { handleVocabulary } from './features/vocabularyExplanation';
import { removeReferenceMarkers } from './features/referenceMarkerRemover';

// 导入重构后的功能模块
import { handleKeywordExtraction } from './features/keywordExtraction';
import { generateSummaryForSelection } from './features/selectionSummary';
import { updateNoteSummary } from './features/summaryGeneration';
import { interpretSelectedText } from './features/textInterpretation';
import { generateStructuredKnowledge } from './features/structuredKnowledgeGeneration';
import { addToThoughtsFlow } from './features/thoughtsFlowLogger';
import { reverseNoteReferences } from './features/noteReferenceReverser';
import { addToTaskLog } from './features/taskLogger';
import { createConceptLink } from './features/conceptLinkCreation';
import { mergeToTimestampNote } from './features/timestampNote';
import { handleTaskPanelSync } from './features/taskManager';
import { handleRAContentSync, previewRAContent } from './features/raContentSync';
import { handleScheduleProcessing } from './features/scheduleManager';

export default class SummaryPlugin extends Plugin {
	settings: MyPluginSettings;
	private console: Console;

	async onload() {
		await this.loadSettings();
		
		// 初始化控制台
		this.console = console;

		console.log('loading summary plugin');
		new Notice('摘要插件已加载');

		// ===== Canvas导出功能 =====
		// 注册Canvas导出命令
		this.addCommand({
			id: 'export-canvas-as-text',
			name: '导出当前Canvas为TXT文件',
			checkCallback: (checking: boolean) => {
				// 检查当前视图是否为Canvas视图
				const canvasView = this.app.workspace.getActiveViewOfType(ItemView);
				if (canvasView && canvasView.getViewType() === 'canvas') {
					if (!checking) {
						new Notice('正在导出Canvas...');
						const exporter = new CanvasExporter(this.app, this);
						// 使用async/await处理异步函数
						exporter.exportCanvasAsText().catch(error => {
							console.error('导出Canvas时出错:', error);
							new Notice('导出Canvas失败: ' + (error as Error).message);
						});
					}
					return true;
				}
				return false;
			}
		});

		// 注册选中节点导出命令
		this.addCommand({
			id: 'export-selected-canvas-nodes-as-text',
			name: '导出Canvas选中节点为TXT文件',
			checkCallback: (checking: boolean) => {
				// 检查当前视图是否为Canvas视图
				const canvasView = this.app.workspace.getActiveViewOfType(ItemView);
				if (canvasView && canvasView.getViewType() === 'canvas') {
					if (!checking) {
						new Notice('正在导出选中的节点...');
						const exporter = new CanvasExporter(this.app, this);
						// 使用async/await处理异步函数
						exporter.exportSelectedNodesAsText().catch(error => {
							console.error('导出选中节点时出错:', error);
							new Notice('导出选中节点失败: ' + (error as Error).message);
						});
					}
					return true;
				}
				return false;
			}
		});

		// 在文件菜单中添加Canvas导出选项以及原有的菜单项
		this.registerEvent(
			this.app.workspace.on('file-menu', (menu, file) => {
				// 检查当前是否是画布文件
				if (file instanceof TFile && file.extension === 'canvas') {
					// 添加导出整个画布的菜单项
					menu.addItem((item) => {
						item.setTitle('🧸 导出画布为TXT文件')
							.setIcon('document')
							.onClick(async () => {
								new Notice('正在导出画布...');
								const exporter = new CanvasExporter(this.app, this);
								try {
									await exporter.exportCanvasAsText();
								} catch (error) {
									console.error('导出画布时出错:', error);
									new Notice('导出画布失败: ' + (error as Error).message);
								}
							});
					});
				}
				
				if (!(file instanceof TFile)) {
					return;
				}
				
				// 生成摘要选项
				menu.addItem((item) => {
					item
						.setTitle('✨ 生成笔记摘要')
						.setIcon('sparkles')
						.onClick(async () => {
							new Notice('点击了文件菜单的生成摘要');
							const view = this.app.workspace.getActiveViewOfType(MarkdownView);
							if (!view || !view.file) {
								new Notice('未找到活动的 Markdown 视图');
								return;
							}
							await updateNoteSummary({
								editor: view.editor,
								file: view.file,
								app: this.app,
								settings: this.settings
							});
						});
				});

				// 复制摘要链接选项
				menu.addItem((item) => {
					item
						.setTitle('⚡️ 复制摘要链接')
						.setIcon('link')
						.onClick(async () => {
							await copySummaryLink({ app: this.app, file });
						});
				});

				// 移动到存档选项
				menu.addItem((item) => {
					item
						.setTitle('🏕️ 移动到存档')
						.setIcon('folder')
						.onClick(async () => {
							await moveToArchive({ 
								app: this.app, 
								settings: { archivePath: this.settings.archivePath },
								file 
							});
						});
				});

				// 添加图片链接处理选项
				menu.addItem((item) => {
					item
						.setTitle('🖼️ 转换图片为相对附件链接')
						.setIcon('image')
						.onClick(async () => {
							const view = this.app.workspace.getActiveViewOfType(MarkdownView);
							if (!view || !view.file) {
								new Notice('未找到活动的 Markdown 视图');
								return;
							}
							await handleImageLinks({ editor: view.editor });
						});
				});

				// 生成知识结构选项
				menu.addItem((item) => {
					item
						.setTitle('📦 生成大纲')
						.setIcon('package')
						.onClick(async () => {
							const view = this.app.workspace.getActiveViewOfType(MarkdownView);
							if (!view || !view.file) {
								new Notice('未找到活动的 Markdown 视图');
								return;
							}
							await generateStructuredKnowledge({
								editor: view.editor,
								file: view.file,
								settings: this.settings
							});
						});
				});

				// 添加导出知识文档选项
				menu.addItem((item) => {
					item
						.setTitle('📚 导出知识文档')
						.setIcon('file-text')
						.onClick(async () => {
							await processNoteLinks({ app: this.app });
						});
				});

				// 添加整理笔记选项
				menu.addItem((item) => {
					item
						.setTitle('📂 整理笔记到主题文件夹')
						.setIcon('folder')
						.onClick(async () => {
							await handleNoteClassification({
								app: this.app,
								settings: {
									apiEndpoint: this.settings.apiEndpoint,
									apiKey: this.settings.apiKey
								}
							});
						});
				});

				// 添加任务面板同步选项
				menu.addItem((item) => {
					item
						.setTitle('🔄 同步任务面板')
						.setIcon('refresh-cw')
						.onClick(async () => {
							await handleTaskPanelSync({
								app: this.app,
								settings: this.settings
							});
						});
				});

				// 添加 RA 内容同步选项
				menu.addItem((item) => {
					item
						.setTitle('🔗 同步 RA 内容到专题笔记')
						.setIcon('git-branch')
						.onClick(async () => {
							const view = this.app.workspace.getActiveViewOfType(MarkdownView);
							if (!view || !view.file) {
								new Notice('未找到活动的 Markdown 视图');
								return;
							}
							await handleRAContentSync({
								app: this.app,
								editor: view.editor,
								file: view.file
							});
						});
				});
			})
		);

		// 为编辑器菜单添加Canvas节点导出选项
		this.registerEvent(
			this.app.workspace.on('editor-menu', (menu, editor, view) => {
				// 检查视图是否为Canvas视图
				// @ts-ignore Canvas视图类型检查，在不同Obsidian版本中可能有所不同
				if (view && typeof view.getViewType === 'function' && view.getViewType() === 'canvas') {
					// 获取Canvas导出器
					const exporter = new CanvasExporter(this.app, this);
					
					// 检查是否有选中的节点
					// @ts-ignore
					const selectedNodes = exporter.getSelectedNodes(view);
					if (selectedNodes && selectedNodes.length > 0) {
						menu.addItem((item) => {
							item.setTitle('导出选中节点为TXT文件')
								.setIcon('document')
								.onClick(async () => {
									new Notice('正在导出选中的节点...');
									try {
										await exporter.exportSelectedNodesAsText();
									} catch (error) {
										console.error('导出选中节点时出错:', error);
										new Notice('导出选中节点失败: ' + (error as Error).message);
									}
								});
						});
					}
				}
			})
		);

		// 添加图片链接处理命令
		this.addCommand({
			id: 'convert-image-links',
			name: '转换图片链接为相对路径',
			editorCallback: (editor: Editor) => {
				handleImageLinks({ editor });
			}
		});

		// 添加导出知识文档选项
		this.addCommand({
			id: 'export-knowledge-document',
			name: '📚 导出知识文档',
			checkCallback: (checking: boolean) => {
				const view = this.app.workspace.getActiveViewOfType(MarkdownView);
				if (view && view.file) {
					if (!checking) {
						processNoteLinks({ app: this.app });
					}
					return true;
				}
				return false;
			}
		});

		// 添加生成/更新知识结构树选项
		this.addCommand({
			id: 'generate-knowledge-structure',
			name: '🌳 生成/更新知识结构树',
			editorCallback: (editor: Editor) => {
				handleKnowledgeStructure({ 
					editor, 
					settings: {
						apiEndpoint: this.settings.apiEndpoint,
						apiKey: this.settings.apiKey
					}
				});
			}
		});

		// 添加整理笔记选项
		this.addCommand({
			id: 'classify-notes',
			name: '📊 对笔记进行分类',
			callback: () => handleNoteClassification({ 
				app: this.app, 
				settings: {
					apiEndpoint: this.settings.apiEndpoint,
					apiKey: this.settings.apiKey
				}
			})
		});

		// 添加提取主题键并组合命令
		this.addCommand({
			id: 'extract-keywords-for-selection',
			name: '🔑 提取选中内容的主题键并组合',
			editorCheckCallback: (checking: boolean, editor: Editor, view: MarkdownView) => {
				const selection = editor.getSelection();
				if (selection) {
					if (!checking) {
						handleKeywordExtraction({
							editor,
							settings: this.settings
						});
					}
					return true;
				}
				return false;
			},
		});

		// 添加生成选中内容摘要命令
		this.addCommand({
			id: 'generate-summary-for-selection',
			name: '✨ 为选中内容生成摘要',
			editorCheckCallback: (checking: boolean, editor: Editor, view: MarkdownView) => {
				const selection = editor.getSelection();
				if (selection) {
					if (!checking) {
						generateSummaryForSelection({
							editor,
							settings: this.settings
						});
					}
					return true;
				}
				return false;
			},
		});

		// 设置选项卡
		this.addSettingTab(new SummarySettingTab(this.app, this));

		// 注册编辑器菜单（右键菜单）
		this.registerEvent(
			this.app.workspace.on('editor-menu', (menu, editor, view) => {
				if (!(view instanceof MarkdownView)) {
					return;
				}

				const selection = editor.getSelection().trim();
				if (!selection) {
					return;
				}

				// 添加解释文本选项
				menu.addItem((item) => {
					item
						.setTitle('👨🏼‍💻 解释这里')
						.setIcon('message-square')
						.onClick(async () => {
							new Notice('正在解释选中的文本...');
							await interpretSelectedText({
								editor,
								settings: this.settings,
								selectedText: selection
							});
						});
				});

				// 添加提取主题键选项
				menu.addItem((item) => {
					item
						.setTitle('🔑 提取主题键')
						.setIcon('key')
						.onClick(async () => {
							new Notice('正在提取主题键...');
							await handleKeywordExtraction({
								editor,
								settings: this.settings
							});
						});
				});

				// 添加解释单词选项
				menu.addItem((item) => {
					item
						.setTitle('📚 解释单词')
						.setIcon('book')
						.onClick(async () => {
							new Notice('正在处理选中的单词...');
							await handleVocabulary({
								editor,
								app: this.app,
								settings: {
									apiEndpoint: this.settings.apiEndpoint,
									apiKey: this.settings.apiKey,
									vocabularyPath: this.settings.vocabularyPath
								},
								word: selection
							});
						});
				});

				// 添加任务日志选项
				menu.addItem((item) => {
					item
						.setTitle('📝 添加到任务日志')
						.setIcon('check-square')
						.onClick(async () => {
							new Notice('正在添加到任务日志...');
							await addToTaskLog({
								editor,
								app: this.app,
								settings: this.settings,
								selectedContent: selection
							});
						});
				});

				// 添加想法瀑布流选项
				menu.addItem((item) => {
					item
						.setTitle('💡 添加到想法瀑布流')
						.setIcon('lightbulb')
						.onClick(async () => {
							new Notice('正在添加到想法瀑布流...');
							await addToThoughtsFlow({
								editor,
								app: this.app,
								settings: this.settings,
								selectedContent: selection
							});
						});
				});

				// 添加概念链接选项
				menu.addItem((item) => {
					item
						.setTitle('🔗 创建概念链接')
						.setIcon('link')
						.onClick(async () => {
							await createConceptLink({
								editor,
								app: this.app,
								settings: this.settings,
								selectedText: selection
							});
						});
				});

				// 添加生成/更新知识结构树选项
				menu.addItem((item) => {
					item
						.setTitle('🌳 生成知识结构树')
						.setIcon('git-branch')
						.onClick(async () => {
							new Notice('正在处理知识结构...');
							await handleKnowledgeStructure({ 
								editor,
								settings: {
									apiEndpoint: this.settings.apiEndpoint,
									apiKey: this.settings.apiKey
								}
							});
						});
				});

				// 添加移除引用标记选项 (编辑器菜单)
				menu.addItem((item) => {
					item
						.setTitle('🗑️ 移除引用标记')
						.setIcon('trash')
						.onClick(async () => {
							const selection = editor.getSelection();
							if (selection.length > 0) {
								const { newText, didChange } = removeReferenceMarkers(selection);
								if (didChange) {
									editor.replaceSelection(newText);
									console.log('选中文本中的引用标记已移除。');
								} else {
									console.log('在选中文本中未找到格式如 "[数字]" 的引用标记。');
								}
							} else {
								console.log('请先选择需要移除引用标记的文本。');
							}
						});
				});

				// 添加生成选中内容摘要选项
				menu.addItem((item) => {
					item
						.setTitle('✨ 生成选中内容摘要')
						.setIcon('heading')
						.onClick(async () => {
							new Notice('正在准备生成摘要...');
							await generateSummaryForSelection({
								editor,
								settings: this.settings
							});
						});
				});

				// 添加日程处理选项
				menu.addItem((item) => {
					item
						.setTitle('⏰ 处理日程安排')
						.setIcon('calendar')
						.onClick(async () => {
							new Notice('正在处理日程安排...');
							await handleScheduleProcessing({
								editor
							});
						});
				});
			})
		);

		// 添加命令面板命令
		// 1. 生成笔记摘要命令
		this.addCommand({
			id: 'generate-note-summary',
			name: '✨ 生成笔记摘要',
			checkCallback: (checking: boolean) => {
				const view = this.app.workspace.getActiveViewOfType(MarkdownView);
				if (view && view.file) {
					if (!checking) {
						new Notice('通过命令面板执行生成摘要');
						updateNoteSummary({
							editor: view.editor,
							file: view.file,
							app: this.app,
							settings: this.settings
						});
					}
					return true;
				}
				return false;
			}
		});

		// 添加反转笔记引用命令
		this.addCommand({
			id: 'reverse-note-references',
			name: '🔄 反转笔记引用格式',
			editorCallback: (editor: Editor) => {
				reverseNoteReferences({ 
					editor,
					app: this.app
				});
			}
		});

		// 2. 复制摘要链接命令
		this.addCommand({
			id: 'copy-summary-link',
			name: '📋 复制摘要链接',
			checkCallback: (checking: boolean) => {
				const view = this.app.workspace.getActiveViewOfType(MarkdownView);
				if (view && view.file) {
					if (!checking) {
						new Notice('通过命令面板执行复制摘要链接');
						copySummaryLink({ app: this.app, file: view.file });
					}
					return true;
				}
				return false;
			}
		});

		// 3. 合并到时间戳笔记命令
		this.addCommand({
			id: 'merge-to-timestamp-note',
			name: '📥 合并到时间戳笔记',
			checkCallback: (checking: boolean) => {
				const view = this.app.workspace.getActiveViewOfType(MarkdownView);
				if (view && view.editor) {
					if (!checking) {
						new Notice('通过命令面板执行合并到时间戳笔记');
						const selection = view.editor.getSelection();
						mergeToTimestampNote({
							editor: view.editor,
							app: this.app,
							selectedContent: selection // 可以是空字符串，现在函数会处理这种情况
						});
					}
					return true;
				}
				return false;
			}
		});

		// 4. 创建概念链接命令
		this.addCommand({
			id: 'create-concept-link',
			name: '🔗 创建概念链接',
			checkCallback: (checking: boolean) => {
				const view = this.app.workspace.getActiveViewOfType(MarkdownView);
				if (view && view.editor) {
					const selection = view.editor.getSelection().trim();
					if (selection) {
						if (!checking) {
							new Notice('正在创建概念链接...');
							createConceptLink({
								editor: view.editor,
								app: this.app,
								settings: this.settings,
								selectedText: selection
							});
						}
						return true;
					}
				}
				return false;
			}
		});

		// 5. 美化选中文本命令
		this.addCommand({
			id: 'beautify-selected-text',
			name: '👨🏼‍💻 解释这里',
			checkCallback: (checking: boolean) => {
				const view = this.app.workspace.getActiveViewOfType(MarkdownView);
				if (view && view.editor) {
					const selection = view.editor.getSelection();
					if (selection) {
						if (!checking) {
							new Notice('正在解释选中的文本...');
							interpretSelectedText({
								editor: view.editor,
								settings: this.settings,
								selectedText: selection
							});
						}
						return true;
					}
				}
				return false;
			}
		});

		// 4. 移动到存档命令
		this.addCommand({
			id: 'move-to-archive',
			name: '🏕️ 移动到存档',
			checkCallback: (checking: boolean) => {
				const view = this.app.workspace.getActiveViewOfType(MarkdownView);
				if (view && view.file) {
					if (!checking) {
						new Notice('通过命令面板执行移动到存档');
						moveToArchive({ 
							app: this.app, 
							settings: { archivePath: this.settings.archivePath },
							file: view.file 
						});
					}
					return true;
				}
				return false;
			}
		});

		// 6. 单词解释命令
		this.addCommand({
			id: 'explain-vocabulary',
			name: '📚 解释单词',
			checkCallback: (checking: boolean) => {
				const view = this.app.workspace.getActiveViewOfType(MarkdownView);
				if (view && view.editor) {
					const selection = view.editor.getSelection().trim();
					if (selection && selection.length > 0) {
						if (!checking) {
							new Notice('正在处理选中的单词...');
							handleVocabulary({
								app: this.app,
								editor: view.editor,
								word: selection,
								settings: {
									apiEndpoint: this.settings.apiEndpoint,
									apiKey: this.settings.apiKey,
									vocabularyPath: this.settings.vocabularyPath
								}
							});
						}
						return true;
					}
				}
				return false;
			}
		});

		// 生成知识结构命令
		this.addCommand({
			id: 'generate-structured-knowledge',
			name: '📦 生成知识结构',
			checkCallback: (checking: boolean) => {
				const view = this.app.workspace.getActiveViewOfType(MarkdownView);
				if (view && view.file) {
					if (!checking) {
						new Notice('通过命令面板执行生成知识结构');
						generateStructuredKnowledge({
							editor: view.editor,
							file: view.file,
							settings: this.settings
						});
					}
					return true;
				}
				return false;
			}
		});

		// 在命令面板注册部分添加新命令
		this.addCommand({
			id: 'insert-time-block',
			name: '⏰ 插入时间块',
			editorCallback: (editor: Editor) => {
				insertTimeBlock({ editor });
			}
		});

		// 添加设置选项卡
		this.addSettingTab(new SummarySettingTab(this.app, this));

		// 添加整理笔记选项
		this.addCommand({
			id: 'classify-notes-folder',
			name: '📂 整理笔记到主题文件夹',
			checkCallback: (checking: boolean) => {
				const view = this.app.workspace.getActiveViewOfType(MarkdownView);
				if (view && view.file) {
					if (!checking) {
						handleNoteClassification({ 
							app: this.app, 
							settings: {
								apiEndpoint: this.settings.apiEndpoint,
								apiKey: this.settings.apiKey
							}
						});
					}
					return true;
				}
				return false;
			}
		});

		// 添加生成选中内容摘要命令
		this.addCommand({
			id: 'generate-summary-for-selection-2',
			name: '✨ 为选中内容生成摘要',
			editorCheckCallback: (checking: boolean, editor: Editor, view: MarkdownView) => {
				const selection = editor.getSelection();
				if (selection) {
					if (!checking) {
						generateSummaryForSelection({
							editor,
							settings: this.settings
						});
					}
					return true;
				}
				return false;
			},
		});

		// 设置选项卡
		this.addSettingTab(new SummarySettingTab(this.app, this));

		// 加载设置
		await this.loadSettings();

		// Canvas导出功能
		this.addCommand({
			id: 'export-canvas-as-text-2',
			name: '导出当前Canvas为TXT文件',
			checkCallback: (checking: boolean) => {
				// 检查当前视图是否为Canvas视图
				const canvasView = this.app.workspace.getActiveViewOfType(ItemView);
				if (canvasView && canvasView.getViewType() === 'canvas') {
					if (!checking) {
						new Notice('正在导出Canvas...');
						const exporter = new CanvasExporter(this.app, this);
						// 使用async/await处理异步函数
						exporter.exportCanvasAsText().catch(error => {
							console.error('导出Canvas时出错:', error);
							new Notice('导出Canvas失败: ' + (error as Error).message);
						});
					}
					return true;
				}
				return false;
			}
		});

		this.addCommand({
			id: 'export-selected-canvas-nodes-as-text-2',
			name: '导出Canvas选中节点为TXT文件',
			checkCallback: (checking: boolean) => {
				// 检查当前视图是否为Canvas视图
				const canvasView = this.app.workspace.getActiveViewOfType(ItemView);
				if (canvasView && canvasView.getViewType() === 'canvas') {
					if (!checking) {
						new Notice('正在导出选中的Canvas节点...');
						const exporter = new CanvasExporter(this.app, this);
						// 使用async/await处理异步函数
						exporter.exportSelectedNodesAsText().catch(error => {
							console.error('导出选中节点时出错:', error);
							new Notice('导出选中节点失败: ' + (error as Error).message);
						});
					}
					return true;
				}
				return false;
			}
		});

		// 监听菜单事件并添加Canvas导出选项
		this.registerEvent(
			// @ts-ignore - canvas-menu 事件在官方类型定义中不存在，但实际存在
			this.app.workspace.on('canvas-menu', (menu: Menu) => {
				const exporter = new CanvasExporter(this.app, this);
				exporter.addToCanvasMenu(menu);
			})
		);

		// 添加一个右键菜单事件监听，处理选中节点的导出
		this.registerEvent(
			this.app.workspace.on('editor-menu', (menu: Menu, editor: Editor, view: any) => {
				if (view && view.getViewType() === 'canvas') {
					const exporter = new CanvasExporter(this.app, this);
					exporter.addToCanvasMenu(menu);
				}
			})
		);

		// 在onload方法中第二个editor-menu注册后添加以下内容
		// ... existing code ...
		this.addCommand({
			id: 'create-note-outline',
			name: '✍️ 创建笔记大纲',
			editorCheckCallback: (checking: boolean, editor: Editor, view: MarkdownView) => {
				const selection = editor.getSelection();
				if (selection) {
					if (!checking) {
						handleCreateNoteOutline({
							editor,
							settings: this.settings
						});
					}
					return true;
				}
				return false;
			},
		});
		
		// 添加生成摘要目录命令
		this.addCommand({
			id: 'generate-abstract-directory',
			name: '📋 生成摘要目录',
			editorCallback: async (editor: Editor, view: MarkdownView) => {
				if (view.file) {
					new Notice('正在生成摘要目录...');
					try {
						await generateAbstractDirectory({
							editor,
							file: view.file
						});
					} catch (error) {
						console.error("生成摘要目录时出错:", error);
						new Notice(`生成摘要目录失败: ${error.message}`);
					}
				} else {
					new Notice('无法获取当前文件。');
				}
			},
		});

		// 在编辑器菜单中添加创建笔记大纲选项
		this.registerEvent(
			this.app.workspace.on('editor-menu', (menu, editor, view) => {
				if (!(view instanceof MarkdownView)) {
					return;
				}

				const selection = editor.getSelection().trim();
				if (!selection) {
					return;
				}

				// 添加创建笔记大纲选项
				menu.addItem((item) => {
					item
						.setTitle('✍️ 创建笔记大纲')
						.setIcon('list-tree')
						.onClick(async () => {
							new Notice('正在准备创建笔记大纲...');
							await handleCreateNoteOutline({
								editor,
								settings: this.settings
							});
						});
				});
				
				// 添加生成摘要目录选项
				menu.addItem((item) => {
					item
						.setTitle('📋 生成摘要目录')
						.setIcon('list-bullet')
						.onClick(async () => {
							if (view.file) {
								new Notice('正在生成摘要目录...');
								try {
									await generateAbstractDirectory({
										editor,
										file: view.file
									});
								} catch (error) {
									console.error("生成摘要目录时出错:", error);
									new Notice(`生成摘要目录失败: ${error.message}`);
								}
							} else {
								new Notice('无法获取当前文件。');
							}
						});
				});
			})
		);

		// 添加移除引用标记命令 (命令面板)
		this.addCommand({
			id: 'remove-reference-markers',
			name: '🗑️ 移除引用标记',
			editorCallback: async (editor: Editor, view: MarkdownView) => {
				const selection = editor.getSelection();
				let originalText = selection;
				let isSelection = true;

				if (!selection) {
					originalText = editor.getValue();
					isSelection = false;
				}

				const { newText, didChange } = removeReferenceMarkers(originalText);

				if (didChange) {
					if (isSelection) {
						editor.replaceSelection(newText);
					} else {
						editor.setValue(newText);
					}
					console.log('引用标记已移除。');
				} else {
					console.log('在当前处理的文本（选中内容或整个笔记）中未找到格式如 "[数字]" 的引用标记。');
				}
			}
		});

		// 添加任务面板同步命令
		this.addCommand({
			id: 'sync-task-panel',
			name: '🔄 同步任务面板',
			callback: async () => {
				await handleTaskPanelSync({
					app: this.app,
					settings: this.settings
				});
			}
		});

		// 添加 RA 内容同步命令
		this.addCommand({
			id: 'sync-ra-content',
			name: '🔗 同步 RA 内容到专题笔记',
			checkCallback: (checking: boolean) => {
				const view = this.app.workspace.getActiveViewOfType(MarkdownView);
				if (view && view.file) {
					if (!checking) {
						handleRAContentSync({
							app: this.app,
							editor: view.editor,
							file: view.file
						});
					}
					return true;
				}
				return false;
			}
		});

		// 添加 RA 内容预览命令
		this.addCommand({
			id: 'preview-ra-content',
			name: '👁️ 预览 RA 内容',
			checkCallback: (checking: boolean) => {
				const view = this.app.workspace.getActiveViewOfType(MarkdownView);
				if (view && view.file) {
					if (!checking) {
						previewRAContent({
							app: this.app,
							editor: view.editor,
							file: view.file
						});
					}
					return true;
				}
				return false;
			}
		});

		// 添加日程处理命令
		this.addCommand({
			id: 'process-schedule',
			name: '⏰ 处理日程安排',
			editorCheckCallback: (checking: boolean, editor: Editor, view: MarkdownView) => {
				const selection = editor.getSelection();
				if (selection.trim()) {
					if (!checking) {
						handleScheduleProcessing({ editor });
					}
					return true;
				}
				return false;
			}
		});
	}

	onunload() {
		console.log('unloading summary plugin');
		new Notice('摘要插件已卸载');
	}

	async loadSettings() {
		this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
	}

	async saveSettings() {
		await this.saveData(this.settings);
	}
}
