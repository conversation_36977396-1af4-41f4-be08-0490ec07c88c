import { App, Notice, TFile, Menu, ItemView, Plugin } from 'obsidian';

/**
 * Canvas节点接口定义
 */
interface CanvasNode {
    id: string;
    type: string; // 'text', 'file', 'link', 'group'
    text?: string;
    file?: string;
    url?: string;
    label?: string;
    x: number;
    y: number;
    width: number;
    height: number;
    color?: string;
}

/**
 * Canvas边接口定义
 */
interface CanvasEdge {
    id: string;
    fromNode: string;
    fromSide?: string; // 'top', 'right', 'bottom', 'left'
    fromEnd?: string; // 'none', 'arrow'
    toNode: string;
    toSide?: string; // 'top', 'right', 'bottom', 'left'
    toEnd?: string; // 'none', 'arrow' (默认)
    color?: string;
    label?: string;
}

/**
 * Canvas文件数据结构
 */
interface CanvasData {
    nodes: CanvasNode[];
    edges: CanvasEdge[];
}

/**
 * Canvas导出功能类
 */
export class CanvasExporter {
    private app: App;
    private plugin: Plugin;

    constructor(app: App, plugin: Plugin) {
        this.app = app;
        this.plugin = plugin;
    }

    /**
     * 从Canvas视图获取当前Canvas文件
     */
    private getActiveCanvasFile(): TFile | null {
        // 尝试获取活动视图
        const activeView = this.app.workspace.getActiveViewOfType(ItemView);
        
        // 检查是否是Canvas视图
        if (activeView && activeView.getViewType() === 'canvas') {
            // 如果是Canvas视图，尝试获取关联文件
            // @ts-ignore - Canvas视图类型不在官方类型定义中
            return activeView.file;
        }
        
        // 备选方案：尝试从当前活动文件获取
        const activeFile = this.app.workspace.getActiveFile();
        if (activeFile && activeFile.extension === 'canvas') {
            return activeFile;
        }
        
        return null;
    }

    /**
     * 获取指定Canvas文件的数据
     */
    private async getCanvasData(canvasFile: TFile): Promise<CanvasData | null> {
        try {
            // 读取Canvas文件内容
            const content = await this.app.vault.read(canvasFile);
            
            // 解析JSON数据
            return JSON.parse(content) as CanvasData;
        } catch (error) {
            console.error('读取或解析Canvas文件时出错:', error);
            new Notice('读取或解析Canvas文件时出错: ' + (error as Error).message);
            return null;
        }
    }

    /**
     * 获取文件节点引用的内容
     */
    private async getFileNodeContent(filePath: string): Promise<string> {
        try {
            console.info(`正在获取文件内容: ${filePath}`);
            
            // 获取文件对象
            const file = this.app.vault.getAbstractFileByPath(filePath);
            
            // 检查是否找到文件且为TFile类型
            if (file instanceof TFile) {
                console.info(`找到文件: ${file.path}`);
                
                // 读取文件内容
                const content = await this.app.vault.read(file);
                
                // 检查内容是否为空
                if (!content || content.trim() === '') {
                    console.warn(`文件内容为空: ${filePath}`);
                    return `[文件 ${filePath} 内容为空]`;
                }
                
                console.info(`成功读取文件内容，长度: ${content.length}`);
                return content;
            } else {
                console.warn(`未找到文件或不是TFile类型: ${filePath}`);
                
                // 尝试解析绝对路径
                if (filePath.startsWith('/')) {
                    // 尝试移除开头的斜杠
                    const alternativePath = filePath.substring(1);
                    const alternativeFile = this.app.vault.getAbstractFileByPath(alternativePath);
                    
                    if (alternativeFile instanceof TFile) {
                        console.info(`使用替代路径找到文件: ${alternativeFile.path}`);
                        const content = await this.app.vault.read(alternativeFile);
                        return content;
                    }
                }
                
                return `[错误: 文件 ${filePath} 未找到]`;
            }
        } catch (error) {
            console.error(`读取文件 ${filePath} 时出错:`, error);
            return `[错误: 无法读取文件 ${filePath}: ${(error as Error).message}]`;
        }
    }

    /**
     * 获取Canvas中当前选中的节点
     */
    public getSelectedNodes(activeView: any): string[] | null {
        try {
            // @ts-ignore - Canvas视图类型不在官方类型定义中
            // 尝试从Canvas视图获取选中的节点
            if (activeView.canvas && activeView.canvas.selection) {
                // 返回选中节点的ID列表
                return Array.from(activeView.canvas.selection.keys());
            }
            return null;
        } catch (error) {
            console.error('获取选中节点时出错:', error);
            return null;
        }
    }

    /**
     * 根据节点ID筛选相关的边
     */
    private filterEdgesByNodes(edges: CanvasEdge[], nodeIds: string[]): CanvasEdge[] {
        // 筛选出起点和终点都在选中节点中的边
        return edges.filter(edge => 
            nodeIds.includes(edge.fromNode) && nodeIds.includes(edge.toNode)
        );
    }

    /**
     * 检查节点是否位于分组内
     */
    private isNodeInGroup(node: CanvasNode, group: CanvasNode): boolean {
        // 检查节点是否完全位于分组节点内部
        return (
            node.x >= group.x &&
            node.y >= group.y &&
            node.x + node.width <= group.x + group.width &&
            node.y + node.height <= group.y + group.height &&
            node.id !== group.id // 确保不把分组本身算作其内部节点
        );
    }

    /**
     * 获取分组内包含的所有节点ID
     */
    private getNodesInGroup(group: CanvasNode, allNodes: CanvasNode[]): string[] {
        return allNodes
            .filter(node => this.isNodeInGroup(node, group))
            .map(node => node.id);
    }

    /**
     * 格式化画布内容为文本
     */
    private async formatCanvasData(canvasData: CanvasData, baseName: string): Promise<string> {
        // 添加导出文件说明区域（固定内容）
        let output = `+------------------------------------------------------------------------------+
|                         OBSIDIAN CANVAS EXPORT FILE                          |
+------------------------------------------------------------------------------+
| 本文件由Obsidian Canvas导出插件生成，包含画布中的节点内容和拓扑结构。          |
| 用途：提供给AI或团队成员作为上下文，了解项目相关的业务逻辑和关系图。           |
|                                                                              |
| Obsidian Canvas是一个强大的可视化笔记组织工具，允许用户在无限画布上排布        |
| 笔记、图片等多种元素，并通过连线构建它们之间的关系。当用户管理项目代码         |
| 相关的业务逻辑片段时，需要能够方便地导出画布中的全部内容和拓扑结构，          |
| 以作为上下文信息提供给AI进行分析或处理。                                      |
|                                                                              |
| 文件结构：                                                                   |
| 1. 节点内容 - 包含画布中所有节点的文字内容                                    |
| 2. 连接关系 - 使用箭头表示节点之间的指向关系和标注                            |
| 3. 分组信息 - 展示节点的归属分组关系                                          |
+------------------------------------------------------------------------------+

`;

        // 添加导出信息
        output += `## ${baseName} Canvas Export\n`;
        output += `Generated: ${new Date().toLocaleString()}\n\n`;

        // 添加节点信息
        output += "## Nodes:\n\n";
        for (const node of canvasData.nodes) {
            // 节点ID和类型以注释形式展示，减少干扰
            output += `<!-- Node ID: ${node.id}, Type: ${node.type} -->\n`;
            
            if (node.type === 'group' && node.label) {
                // 分组节点 - 以标题形式展示
                output += `### 分组: ${node.label}\n\n`;
                
                // 获取分组内的节点ID
                const containedNodeIds = this.getNodesInGroup(node, canvasData.nodes);
                if (containedNodeIds.length > 0) {
                    output += `包含节点: ${containedNodeIds.length}个\n\n`;
                }
            } else if (node.type === 'text') {
                // 文本节点 - 直接展示内容，不需要额外键值对
                const title = node.text?.substring(0, 50).split('\n')[0] || 'Untitled';
                output += `### ${title}\n\n`;
                if (node.text) {
                    // 直接展示内容，不添加缩进
                    output += `${node.text}\n\n`;
                }
            } else if (node.type === 'file') {
                // 文件节点 - 显示文件标题和内容，无需键值对
                const fileName = node.file || 'Unknown File';
                output += `### ${fileName}\n\n`;
                
                // 获取文件内容，而不是使用节点中的text属性
                if (node.file) {
                    try {
                        const fileContent = await this.getFileNodeContent(node.file);
                        output += `${fileContent}\n\n`;
                    } catch (error) {
                        console.error(`读取文件 ${node.file} 内容时出错:`, error);
                        output += `[无法读取文件内容: ${error.message}]\n\n`;
                    }
                } else if (node.text) {
                    // 如果没有文件路径但有text属性，显示text内容
                    output += `${node.text}\n\n`;
                }
            } else if (node.type === 'link') {
                // 链接节点 - 简洁显示
                output += `### 链接: ${node.url || 'Unknown URL'}\n\n`;
            }
        }
        
        // 添加连接信息 - 使用ASCII箭头样式展示
        if (canvasData.edges && canvasData.edges.length > 0) {
            output += "## Connections:\n\n";
            for (const edge of canvasData.edges) {
                // 查找源节点和目标节点的名称，使连接更具可读性
                const fromNode = this.findNodeName(edge.fromNode, canvasData.nodes);
                const toNode = this.findNodeName(edge.toNode, canvasData.nodes);
                
                // 构建箭头样式连接
                if (edge.label) {
                    // 有标签的连接: A ---标签---> B
                    output += `${fromNode} ---${edge.label}---> ${toNode}\n`;
                } else {
                    // 无标签的连接: A ---------> B
                    output += `${fromNode} ---------> ${toNode}\n`;
                }
            }
            output += '\n';
        }
        
        // 添加分组拓扑关系信息 - 以更简洁的格式展示
        const groupNodes = canvasData.nodes.filter(node => node.type === 'group');
        if (groupNodes.length > 0) {
            output += "## Groups:\n\n";
            for (const group of groupNodes) {
                const groupName = group.label || `Group ${group.id.substring(0, 6)}`;
                output += `### ${groupName}\n\n`;
                
                // 获取分组内的节点，并显示其名称而非ID
                const containedNodeIds = this.getNodesInGroup(group, canvasData.nodes);
                if (containedNodeIds.length > 0) {
                    // 直接列出组内节点
                    for (const nodeId of containedNodeIds) {
                        const nodeName = this.findNodeName(nodeId, canvasData.nodes);
                        output += `- ${nodeName}\n`;
                    }
                } else {
                    output += `(空分组)\n`;
                }
                output += '\n';
            }
        }
        
        return output;
    }

    /**
     * 根据节点ID查找节点的名称
     */
    private findNodeName(nodeId: string, nodes: CanvasNode[]): string {
        const node = nodes.find(n => n.id === nodeId);
        if (!node) return `未知节点`;
        
        switch (node.type) {
            case 'text':
                // 对于文本节点，使用内容的第一行（如果太长就截断）
                if (node.text) {
                    const firstLine = node.text.split('\n')[0].trim();
                    if (firstLine.length > 40) {
                        return firstLine.substring(0, 37) + '...';
                    }
                    return firstLine;
                }
                return '空文本节点';
                
            case 'file':
                // 对于文件节点，使用文件名但去掉路径部分
                if (node.file) {
                    const fileName = node.file.split('/').pop() || node.file;
                    return fileName;
                }
                return '未命名文件';
                
            case 'group':
                // 对于分组节点，直接使用标签
                return node.label || '未命名分组';
                
            case 'link':
                // 对于链接节点，尝试提取域名或显示简短URL
                if (node.url) {
                    try {
                        const url = new URL(node.url);
                        return url.hostname;
                    } catch (e) {
                        // 如果不是有效URL，显示截断版本
                        return node.url.length > 30 ? node.url.substring(0, 27) + '...' : node.url;
                    }
                }
                return '未命名链接';
                
            default:
                return `未知类型节点`;
        }
    }

    /**
     * 将文本内容保存为文件
     */
    private async saveToFile(content: string, baseName: string): Promise<void> {
        try {
            // 生成时间戳
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
            
            // 构建文件名
            const fileName = `${baseName}_export_${timestamp}.txt`;
            
            // 导出到下载目录
            const exportPath = `/Users/<USER>/Downloads/${fileName}`;
            
            // 使用 Node.js 的 fs 模块写入文件
            const fs = require('fs');
            fs.writeFileSync(exportPath, content);
            
            new Notice(`画布已成功导出到: ${exportPath}`);
        } catch (error) {
            console.error('保存导出文件时出错:', error);
            new Notice('保存导出文件失败: ' + (error as Error).message);
        }
    }

    /**
     * 导出整个Canvas为文本文件
     */
    public async exportCanvasAsText(): Promise<void> {
        try {
            // 获取当前激活的Canvas视图
            const activeView = this.app.workspace.getActiveViewOfType(ItemView);
            if (!activeView || activeView.getViewType() !== 'canvas') {
                new Notice('当前激活的视图不是Canvas');
                return;
            }

            // 获取Canvas文件
            // @ts-ignore - Canvas视图类型未在官方类型定义中正确定义file属性
            const canvasFile = activeView.file;
            if (!canvasFile) {
                new Notice('无法获取Canvas文件');
                return;
            }

            // 读取Canvas文件内容
            const canvasContent = await this.app.vault.read(canvasFile);
            
            // 解析JSON数据
            const canvasData: CanvasData = JSON.parse(canvasContent);
            
            // 格式化Canvas数据为文本
            const formattedText = await this.formatCanvasData(canvasData, canvasFile.basename);
            
            // 保存到文件
            await this.saveToFile(formattedText, canvasFile.basename);
            
            new Notice(`已导出Canvas: ${canvasFile.basename}`);
        } catch (error) {
            console.error('导出Canvas时出错:', error);
            new Notice('导出Canvas失败: ' + (error as Error).message);
        }
    }

    /**
     * 导出选中的Canvas节点为文本文件
     */
    public async exportSelectedNodesAsText(): Promise<void> {
        try {
            // 获取当前激活的Canvas视图
            const activeView = this.app.workspace.getActiveViewOfType(ItemView);
            if (!activeView || activeView.getViewType() !== 'canvas') {
                new Notice('当前激活的视图不是Canvas');
                return;
            }

            // 获取Canvas文件
            // @ts-ignore - Canvas视图类型未在官方类型定义中正确定义file属性
            const canvasFile = activeView.file;
            if (!canvasFile) {
                new Notice('无法获取Canvas文件');
                return;
            }

            // 获取选中的节点ID
            const selectedNodeIds = this.getSelectedNodes(activeView);
            if (!selectedNodeIds || selectedNodeIds.length === 0) {
                new Notice('未选择任何节点');
                return;
            }

            // 读取Canvas文件内容
            const canvasContent = await this.app.vault.read(canvasFile);
            
            // 解析JSON数据
            const canvasData: CanvasData = JSON.parse(canvasContent);
            
            // 过滤仅保留选中的节点和相关的边
            const filteredData: CanvasData = {
                nodes: canvasData.nodes.filter(node => selectedNodeIds.includes(node.id)),
                edges: this.filterEdgesByNodes(canvasData.edges, selectedNodeIds)
            };
            
            // 格式化Canvas数据为文本，只包含选中的节点
            const formattedText = await this.formatCanvasData(filteredData, `${canvasFile.basename}_selected`);
            
            // 保存到文件
            await this.saveToFile(formattedText, `${canvasFile.basename}_selected`);
            
            new Notice(`已导出选中的节点: ${selectedNodeIds.length}个`);
        } catch (error) {
            console.error('导出选中节点时出错:', error);
            new Notice('导出选中节点失败: ' + (error as Error).message);
        }
    }

    /**
     * 向Canvas菜单添加导出选项
     */
    public addToCanvasMenu(menu: Menu): void {
        // 添加导出整个Canvas的菜单项
        menu.addItem((item) => {
            item.setTitle('导出画布为TXT文件')
                .setIcon('document')
                .onClick(async () => {
                    // 显示正在处理的通知
                    new Notice('正在导出画布...');
                    // 使用async/await调用异步方法
                    await this.exportCanvasAsText();
                });
        });
        
        // 检查是否有选中的节点
        const activeView = this.app.workspace.getActiveViewOfType(ItemView);
        if (activeView && activeView.getViewType() === 'canvas') {
            const selectedNodeIds = this.getSelectedNodes(activeView);
            if (selectedNodeIds && selectedNodeIds.length > 0) {
                // 添加导出选中节点的菜单项
                menu.addItem((item) => {
                    item.setTitle('导出选中内容为TXT文件')
                        .setIcon('document')
                        .onClick(async () => {
                            // 显示正在处理的通知
                            new Notice('正在导出选中的节点...');
                            // 使用async/await调用异步方法
                            await this.exportSelectedNodesAsText();
                        });
                });
            }
        }
    }

    /**
     * 对多行文本进行缩进处理
     */
    private indentText(text: string): string {
        return text.split('\n').map(line => `    ${line}`).join('\n');
    }
} 
