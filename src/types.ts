// 插件设置接口
export interface MyPluginSettings {
	apiKey: string;
	apiEndpoint: string;
	archivePath: string;
	vocabularyPath: string;
	taskLogPath: string;
	thoughtsPath: string;
	obsidianRoot: string;
	conceptPath: string;
	// 任务管理相关设置
	taskScanFiles: string;
	taskPanelPath: string;
}

// 摘要响应接口
export interface SummaryResponse {
	title: string;
	summary: string;
	"key-points": string[];
	"action-items": string[];
}

// 笔记节点接口
export interface NoteNode {
	path: string;
	children: NoteNode[];
}

// 知识节点接口
export interface KnowledgeNode {
    layer: string;
    concept_id: number;
    content_or_note_refrence_id: string;
    comment?: string;
    subnode: KnowledgeNode[];
}

// 带摘要的笔记链接接口
export interface NoteLinkWithSummary {
    note_id: string;
    note_title: string;
    summary: string;
}

// 知识结构输入接口
export interface KnowledgeStructureInput {
    existing_knowledge_structure?: KnowledgeNode;
    new_note_links_with_summaries: NoteLinkWithSummary[];
}

// 分类响应接口
export interface ClassificationResponse {
    classifications: Array<{
        note_id: string;
        paths: string[];
    }>;
}

// 主题项接口 (用于重新排序)
export interface TopicItem {
	path: string;
	number: number;
	title: string;
	originalPath: string;
}

// 任务类型枚举
export enum TaskType {
	COMPLETE = 'complete',  // 完整任务：有时间戳且已完成
	PLANNED = 'planned',    // 计划任务：有时间戳但未完成
	NORMAL = 'normal'       // 普通任务：没有时间戳
}

// 任务项接口
export interface TaskItem {
	id: string;           // 时间戳作为唯一标识
	content: string;      // 任务内容
	topic: string;        // 主题（默认为"日常"）
	completed: boolean;   // 完成状态
	taskType: TaskType;   // 任务类型
	sourceFile: string;   // 源文件路径
	sourceLine: number;   // 源文件行号
	indentedContent: string[]; // 缩进的子内容
	originalLine: string; // 原始行内容（用于状态同步）
}

// 任务扫描结果接口
export interface TaskScanResult {
	tasks: TaskItem[];
	errors: string[];
}

// RA 内容项接口
export interface RAContentItem {
	id: string;           // 时间戳作为唯一标识
	timestamp: string;    // 时间戳字符串
	content: string[];    // 内容行数组（从RA标记行到空白行）
	topics: string[];     // 主题数组
	sourceFile: string;   // 源文件路径
	sourceLine: number;   // RA标记行在源文件中的行号
	fullContent: string;  // 完整内容（用于写入专题笔记）
}

// RA 扫描结果接口
export interface RAScanResult {
	raItems: RAContentItem[];
	errors: string[];
}

// 专题笔记信息接口
export interface TopicNoteInfo {
	path: string;         // 文件路径
	number: number;       // 编号
	title: string;        // 标题
	exists: boolean;      // 是否存在
}
