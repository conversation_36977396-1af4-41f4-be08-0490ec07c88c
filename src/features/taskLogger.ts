import { Editor, Notice, TFile, App } from 'obsidian';

// 类型定义
interface TaskLoggerSettings {
	taskLogPath: string;
}

interface TaskLoggerOptions {
	editor: Editor;
	app: App;
	settings: TaskLoggerSettings;
	selectedContent: string;
}

// 主要功能函数
export async function addToTaskLog(options: TaskLoggerOptions): Promise<void> {
	const { editor, app, settings, selectedContent } = options;
	
	try {
		// 获取当前时间并格式化
		const now = new Date();
		const year = now.getFullYear();
		const month = now.getMonth() + 1;
		const day = now.getDate();
		const hour = String(now.getHours()).padStart(2, '0');
		const minute = String(now.getMinutes()).padStart(2, '0');
		
		// 构建格式化的时间戳
		const timestamp = `${year}年${month}月${day}日 ${hour}:${minute}`;

		// 构建要添加的内容，使用复选框格式
		const contentToAdd = `- [ ] \`${timestamp}\` ${selectedContent}\n`;

		// 获取任务日志文件
		const taskLogPath = settings.taskLogPath.startsWith('/') 
			? settings.taskLogPath.slice(1) 
			: settings.taskLogPath;

		// 确保目标文件夹存在
		const folderPath = taskLogPath.substring(0, taskLogPath.lastIndexOf('/'));
		if (!await app.vault.adapter.exists(folderPath)) {
			await app.vault.createFolder(folderPath);
		}

		// 获取或创建任务日志文件
		let taskLogFile = app.vault.getAbstractFileByPath(taskLogPath);
		if (!taskLogFile) {
			taskLogFile = await app.vault.create(taskLogPath, contentToAdd);
		} else if (taskLogFile instanceof TFile) {
			// 读取现有内容
			const existingContent = await app.vault.read(taskLogFile);
			// 在底部添加新内容
			await app.vault.modify(taskLogFile, existingContent + contentToAdd);
		}

		// 在原编辑区域，保留内容，在content上一行添加 #已记录
		editor.replaceSelection(`#已记录\n${selectedContent}`);

		new Notice('✅ 已添加到任务日志！');
	} catch (error) {
		console.error('添加到任务日志时出错:', error);
		new Notice('添加到任务日志失败: ' + error.message);
	}
}
