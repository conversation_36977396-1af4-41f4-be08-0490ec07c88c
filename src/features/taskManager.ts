import { App, Notice } from 'obsidian';
import { MyPluginSettings } from '../types';
import { TaskScanner } from './taskScanner';
import { TaskPanelManager } from './taskPanelManager';
import { TaskSyncManager } from './taskSyncManager';

/**
 * 任务管理器 - 整合任务扫描、面板管理和状态同步功能
 */
export class TaskManager {
    private app: App;
    private settings: MyPluginSettings;
    private scanner: TaskScanner;
    private panelManager: TaskPanelManager;
    private syncManager: TaskSyncManager;

    constructor(app: App, settings: MyPluginSettings) {
        this.app = app;
        this.settings = settings;
        this.scanner = new TaskScanner(app);
        this.panelManager = new TaskPanelManager(app);
        this.syncManager = new TaskSyncManager(app);
    }

    /**
     * 同步任务面板 - 主要的公共接口
     * 以源文件为唯一真实源，任务面板仅作为展示
     * 1. 扫描源文件中的任务（获取最新状态）
     * 2. 更新任务面板（反映源文件状态）
     */
    async syncTaskPanel(): Promise<void> {
        try {
            new Notice('开始同步任务面板...');

            // 1. 扫描任务（源文件为唯一真实源）
            const scanResult = await this.scanner.scanTasks(this.settings.taskScanFiles);

            if (scanResult.errors.length > 0) {
                for (const error of scanResult.errors) {
                    console.error('任务扫描错误:', error);
                    new Notice(`扫描错误: ${error}`);
                }
            }

            if (scanResult.tasks.length === 0) {
                new Notice('未找到任何任务');
                // 即使没有任务也要更新面板，清空内容
                await this.panelManager.updateTaskPanel([], this.settings.taskPanelPath);
                return;
            }

            // 2. 直接更新任务面板（反映源文件的真实状态）
            await this.panelManager.updateTaskPanel(scanResult.tasks, this.settings.taskPanelPath);

            new Notice(`任务面板同步完成！共处理 ${scanResult.tasks.length} 个任务`);

        } catch (error) {
            console.error('同步任务面板时出错:', error);
            new Notice(`同步失败: ${error.message}`);
        }
    }



    /**
     * 获取任务统计信息
     */
    async getTaskStatistics(): Promise<{
        total: number;
        completed: number;
        pending: number;
        byTopic: Map<string, { total: number; completed: number }>;
    }> {
        const scanResult = await this.scanner.scanTasks(this.settings.taskScanFiles);
        const tasks = scanResult.tasks;

        const stats = {
            total: tasks.length,
            completed: tasks.filter(t => t.completed).length,
            pending: tasks.filter(t => !t.completed).length,
            byTopic: new Map<string, { total: number; completed: number }>()
        };

        // 按主题统计
        for (const task of tasks) {
            const topic = task.topic || '日常';
            if (!stats.byTopic.has(topic)) {
                stats.byTopic.set(topic, { total: 0, completed: 0 });
            }
            const topicStats = stats.byTopic.get(topic)!;
            topicStats.total++;
            if (task.completed) {
                topicStats.completed++;
            }
        }

        return stats;
    }

    /**
     * 更新设置
     * @param newSettings 新的设置
     */
    updateSettings(newSettings: MyPluginSettings): void {
        this.settings = newSettings;
    }
}

/**
 * 处理任务面板同步的主要函数
 * @param app Obsidian App 实例
 * @param settings 插件设置
 */
export async function handleTaskPanelSync({
    app,
    settings
}: {
    app: App;
    settings: MyPluginSettings;
}): Promise<void> {
    const taskManager = new TaskManager(app, settings);
    await taskManager.syncTaskPanel();
}
