import { App, TFile } from 'obsidian';
import { TaskItem, TaskType } from '../types';

/**
 * 任务面板管理器 - 负责管理任务面板文件的内容
 */
export class TaskPanelManager {
    private app: App;

    constructor(app: App) {
        this.app = app;
    }

    /**
     * 更新任务面板
     * @param tasks 任务列表
     * @param panelPath 任务面板文件路径
     */
    async updateTaskPanel(tasks: TaskItem[], panelPath: string): Promise<void> {
        if (!panelPath.trim()) {
            throw new Error('任务面板路径未配置');
        }

        // 按主题分组任务
        const groupedTasks = this.groupTasksByTopic(tasks);
        
        // 生成任务面板内容
        const panelContent = this.generatePanelContent(groupedTasks);
        
        // 写入文件
        await this.writeToFile(panelPath, panelContent);
    }

    /**
     * 按主题分组任务
     * @param tasks 任务列表
     * @returns 按主题分组的任务
     */
    private groupTasksByTopic(tasks: TaskItem[]): Map<string, TaskItem[]> {
        const grouped = new Map<string, TaskItem[]>();

        for (const task of tasks) {
            const topic = task.topic || '日常';
            if (!grouped.has(topic)) {
                grouped.set(topic, []);
            }
            grouped.get(topic)!.push(task);
        }

        // 对每个主题内的任务按四级精细优先级排序（已完成任务优先）
        for (const [topic, topicTasks] of grouped) {
            topicTasks.sort((a, b) => this.compareTasksByPriority(a, b));
        }

        return grouped;
    }

    /**
     * 生成任务面板内容
     * @param groupedTasks 按主题分组的任务
     * @returns 面板内容字符串
     */
    private generatePanelContent(groupedTasks: Map<string, TaskItem[]>): string {
        const lines: string[] = [];
        
        // 添加标题
        lines.push('# 任务面板');
        lines.push('');
        lines.push('> 此文件由任务管理插件自动生成，请勿手动编辑');
        lines.push('');

        // 获取所有主题并排序（日常放在最前面）
        const topics = Array.from(groupedTasks.keys()).sort((a, b) => {
            if (a === '日常') return -1;
            if (b === '日常') return 1;
            return a.localeCompare(b);
        });

        for (const topic of topics) {
            const tasks = groupedTasks.get(topic)!;
            
            // 添加主题标题
            lines.push(`## ${topic}`);
            lines.push('');

            // 添加任务
            for (const task of tasks) {
                const checkbox = task.completed ? '[x]' : '[ ]';
                lines.push(`- ${checkbox} ${task.id}｜${task.topic}｜${task.content}`);

                // 添加缩进内容，完全保持原有的缩进格式
                for (const indentLine of task.indentedContent) {
                    // 完全保持原有格式，不做任何修改
                    lines.push(indentLine);
                }

                lines.push('');
            }
        }

        return lines.join('\n');
    }

    /**
     * 写入文件
     * @param filePath 文件路径
     * @param content 文件内容
     */
    private async writeToFile(filePath: string, content: string): Promise<void> {
        let file = this.app.vault.getAbstractFileByPath(filePath);

        if (!file) {
            // 文件不存在，创建新文件
            file = await this.app.vault.create(filePath, content);
        } else if (file instanceof TFile) {
            // 文件存在，更新内容
            await this.app.vault.modify(file, content);
        } else {
            throw new Error(`路径 ${filePath} 不是有效的文件`);
        }
    }

    /**
     * 从任务面板读取任务状态
     * @param panelPath 任务面板文件路径
     * @returns 任务状态映射（任务ID -> 完成状态）
     */
    async readTaskStatesFromPanel(panelPath: string): Promise<Map<string, boolean>> {
        const stateMap = new Map<string, boolean>();

        try {
            const file = this.app.vault.getAbstractFileByPath(panelPath);
            if (!file || !(file instanceof TFile)) {
                return stateMap;
            }

            const content = await this.app.vault.read(file);
            const lines = content.split('\n');

            for (const line of lines) {
                const taskMatch = this.parseTaskLineFromPanel(line);
                if (taskMatch) {
                    stateMap.set(taskMatch.id, taskMatch.completed);
                }
            }
        } catch (error) {
            console.error('读取任务面板状态时出错:', error);
        }

        return stateMap;
    }

    /**
     * 解析任务面板中的任务行
     * @param line 文本行
     * @returns 解析结果或 null
     */
    private parseTaskLineFromPanel(line: string): {
        id: string;
        completed: boolean;
    } | null {
        // 匹配格式：- [ ] 或 - [x] 后跟时间戳｜主题｜内容
        const taskRegex = /^-\s*\[([ x])\]\s*(\d{14})｜/;
        const match = line.match(taskRegex);

        if (!match) {
            return null;
        }

        const [, checkboxState, timestamp] = match;
        
        return {
            id: timestamp,
            completed: checkboxState.toLowerCase() === 'x'
        };
    }

    /**
     * 按四级精细优先级比较任务（已完成任务优先）
     * 1. 完整任务（有时间戳且已完成）- 按时间戳升序
     * 2. 已完成的普通任务（没有时间戳但已完成）- 按内容字母顺序
     * 3. 计划任务（有时间戳但未完成）- 按时间戳升序
     * 4. 未完成的普通任务（没有时间戳且未完成）- 按内容字母顺序
     * @param a 任务A
     * @param b 任务B
     * @returns 比较结果
     */
    private compareTasksByPriority(a: TaskItem, b: TaskItem): number {
        // 1. 获取四级精细优先级
        const aPriority = this.getDetailedTaskPriority(a);
        const bPriority = this.getDetailedTaskPriority(b);

        if (aPriority !== bPriority) {
            return aPriority - bPriority;
        }

        // 2. 同优先级内的排序
        if (aPriority === 1 || aPriority === 3) {
            // 完整任务和计划任务：按时间戳升序排序（早的在前）
            return a.id.localeCompare(b.id);
        } else {
            // 普通任务（已完成和未完成）：按内容字母顺序排序
            return a.content.localeCompare(b.content);
        }
    }

    /**
     * 获取四级精细优先级数值（已完成任务优先）
     * @param task 任务对象
     * @returns 优先级数值（越小优先级越高）
     */
    private getDetailedTaskPriority(task: TaskItem): number {
        switch (task.taskType) {
            case TaskType.COMPLETE:
                // 1. 完整任务（有时间戳且已完成）- 最高优先级
                return 1;
            case TaskType.PLANNED:
                // 3. 计划任务（有时间戳但未完成）- 第三优先级
                return 3;
            case TaskType.NORMAL:
                // 2和4. 普通任务按完成状态细分
                if (task.completed) {
                    // 2. 已完成的普通任务（没有时间戳但已完成）- 第二优先级
                    return 2;
                } else {
                    // 4. 未完成的普通任务（没有时间戳且未完成）- 最低优先级
                    return 4;
                }
            default:
                // 未知类型，最低优先级
                return 5;
        }
    }

    /**
     * 获取任务类型的优先级数值（保留旧方法以兼容性）
     * @param taskType 任务类型
     * @returns 优先级数值（越小优先级越高）
     * @deprecated 使用 getDetailedTaskPriority 替代
     */
    private getTaskTypePriority(taskType: TaskType): number {
        switch (taskType) {
            case TaskType.COMPLETE:
                return 1; // 最高优先级
            case TaskType.PLANNED:
                return 2; // 中等优先级
            case TaskType.NORMAL:
                return 3; // 最低优先级
            default:
                return 4; // 未知类型，最低优先级
        }
    }
}
