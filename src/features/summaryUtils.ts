import { TFile, Notice, App } from 'obsidian';

// 类型定义
interface SummaryUtilsOptions {
	app: App;
	file: TFile;
}

// 主要功能函数
export async function copySummaryLink(options: SummaryUtilsOptions): Promise<void> {
	const { app, file } = options;
	
	try {
		const content = await app.vault.read(file);
		const summaryMatch = content.match(/摘要：(.*?)\n/);
		
		if (!summaryMatch) {
			new Notice('未找到摘要内容');
			return;
		}

		const summary = summaryMatch[1].trim();
		// 支持12位或14位时间戳
		const titleMatch = file.basename.match(/^\d{12,14}｜(.+)$/);
		
		if (!titleMatch) {
			new Notice('文件名格式不正确，应为"时间戳｜标题"格式（时间戳12-14位）');
			return;
		}

		const title = titleMatch[1];
		const linkText = `[[${file.basename}]]｜${summary} `;
		
		await navigator.clipboard.writeText(linkText);
		new Notice('已复制摘要链接！');
	} catch (error) {
		console.error('复制摘要链接时出错:', error);
		new Notice('复制摘要链接失败: ' + error.message);
	}
}
