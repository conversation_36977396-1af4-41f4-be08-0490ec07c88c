import { Notice, App, TFile } from 'obsidian';

// 提示词常量
export const NOTE_CLASSIFICATION_PROMPT = `作为一个专业的知识管理助手，你的任务是将笔记分类到合适的主题领域中。请仔细分析每个笔记的标题和摘要，并将其分配到最合适的主题文件中。

# 输入数据
你将收到两个数据数组：
1. 笔记数组：包含 [{id, title, summary}] 格式的笔记信息
   - id: 12-14位的时间戳
   - title: 笔记标题
   - summary: 笔记摘要（可能为空）
2. 主题数组：现有的主题文件路径列表

# 分类规则
1. 优先使用现有主题进行分类，尽量避免创建新主题
2. 仔细分析现有主题的语义和范围，充分利用现有主题的可扩展性
3. 每个笔记可以同时归属于多个主题（最多3个）
4. 所有主题必须遵循以下路径格式：
   - Areas类型：'02-Areas/XX - 主题名称'（持续性项目和领域）
   - Resources类型：'03-Resources/XX - 主题名称'（参考资料和知识库）
5. 如果笔记摘要为空，主要依据标题进行分类
6. 新主题创建规则（仅在确实无法归类到现有主题时使用）：
   - 必须确认没有任何现有主题可以涵盖该内容
   - 新主题必须具有足够的普适性和可扩展性
   - 持续性项目和领域知识 -> Areas，格式：'NEW: 02-Areas/XX - 主题名称'
   - 参考资料和知识库 -> Resources，格式：'NEW: 03-Resources/XX - 主题名称'

# 主题选择策略
1. 宽松匹配：不要过于严格地要求主题名称与内容完全对应
2. 上位概念：优先考虑将内容归类到更宽泛的现有主题下
3. 相关性：即使不是完全匹配，只要有一定相关性，也可以归类到现有主题
4. 谨慎创建：只有在确实无法找到任何相关现有主题时，才考虑创建新主题
5. 严格遵循现有的主题分类结构，Areas中已有的主题不允许添加和新建到Resources中

# 输出格式
请返回如下格式的 JSON：
{
  "classifications": [
    {
      "note_id": "时间戳",
      "paths": ["主题路径1", "主题路径2"]  // 必须包含完整路径前缀
    }
  ]
}

# 示例输出
{
  "classifications": [
    {
      "note_id": "202501190200",
      "paths": [
        "02-Areas/09 - 提示词",
        "03-Resources/03 - 阅读笔记"
      ]
    }
  ]
}

重要说明：
1. 保持输出格式的一致性和严格性
2. 只返回一个 JSON 对象
3. 确保所有路径都包含完整的前缀（02-Areas/ 或 03-Resources/）
4. 新主题必须以 'NEW:' 开头，并包含完整路径前缀
5. note_id 必须保持原始时间戳格式，不要修改
6. 创建新主题时要特别谨慎，优先考虑使用现有主题`;

// 类型定义
interface ClassificationResponse {
    classifications: Array<{
        note_id: string;
        paths: string[];
    }>;
}

interface NoteClassificationSettings {
	apiEndpoint: string;
	apiKey: string;
}

interface NoteClassificationOptions {
	app: App;
	settings: NoteClassificationSettings;
}

interface TopicItem {
	path: string;
	number: number;
	title: string;
	originalPath: string;
}

// 验证分类响应的内部函数
function isValidClassificationResponse(data: any): data is ClassificationResponse {
	if (!data || typeof data !== 'object') return false;
	if (!Array.isArray(data.classifications)) return false;
	
	return data.classifications.every((item: any) => 
		typeof item.note_id === 'string' && 
		Array.isArray(item.paths) &&
		item.paths.every((path: any) => typeof path === 'string')
	);
}

// 提取摘要函数
function extractSummary(content: string): string {
	// 改进摘要提取逻辑，处理多行摘要
	const summaryMatch = content.match(/摘要：([\s\S]*?)(?:\n\n|$)/);
	return summaryMatch ? summaryMatch[1].trim() : '';
}

// 提取现有笔记链接函数
function extractExistingNoteLinks(content: string): Array<{id: string, title?: string, summary: string}> {
	const noteListSection = content.split('## 笔记列表')[1];
	if (!noteListSection) {
		console.log('[笔记提取] ℹ️ 未找到笔记列表部分');
		return [];
	}

	const lines = noteListSection.split('\n');
	const notesMap = new Map<string, {id: string, title?: string, summary: string}>();
	let duplicateCount = 0;
	
	lines.forEach(line => {
		const match = line.match(/\[\[([^\]]+)\]\](｜|\|)(.+)/);
		if (match) {
			const [, noteId, , summary] = match;
			if (notesMap.has(noteId)) {
				duplicateCount++;
			} else {
				notesMap.set(noteId, {
					id: noteId,
					summary: summary.trim()
				});
			}
		}
	});

	if (duplicateCount > 0) {
		console.log(`[笔记提取] 🔍 发现 ${duplicateCount} 个重复笔记，已自动去重`);
	}
	console.log(`[笔记提取] ✅ 成功提取 ${notesMap.size} 个唯一笔记`);

	return Array.from(notesMap.values());
}

// 检查笔记是否存在函数
function checkNoteExistence(content: string, noteId: string): boolean {
	// 使用正则表达式匹配笔记链接的两种可能格式：
	// 1. [[时间戳｜标题]]｜摘要
	// 2. [[时间戳]]｜摘要
	const noteLinkPatterns = [
		new RegExp(`\\[\\[${noteId}\\|[^\\]]+\\]\\]｜.*`),  // 匹配带标题的格式
		new RegExp(`\\[\\[${noteId}\\]\\]｜.*`)             // 匹配不带标题的格式
	];

	// 如果任一格式匹配成功，则认为笔记存在
	return noteLinkPatterns.some(pattern => pattern.test(content));
}

// 格式化主题名称函数
function formatTopicName(topicName: string): string {
	if (!topicName.match(/^\d{2} - [^/\\:*?"<>|]+$/)) {
		const match = topicName.match(/(\d+)?\s*-?\s*(.+)/);
		if (match) {
			const [_, num, name] = match;
			const formattedNum = num ? 
				String(num).padStart(2, '0') : 
				'01';
			return `${formattedNum} - ${name.trim()}`;
		}
		return `01 - ${topicName}`;
	}
	return topicName;
}

// 添加到笔记列表函数
function appendToNotesList(content: string, noteLink: string): string {
	const sections = content.split('## 笔记列表');
	if (sections.length !== 2) {
		console.log('[笔记列表] 📝 创建新的笔记列表部分');
		return content + '\n\n## 笔记列表\n\n1. ' + noteLink;
	}

	const [header, notesList] = sections;
	const existingNotes = extractExistingNoteLinks(content);
	
	// 检查新笔记是否已存在
	const newNoteMatch = noteLink.match(/\[\[([^\]]+)\]\]/);
	if (newNoteMatch) {
		const newNoteId = newNoteMatch[1];
		if (existingNotes.some(note => note.id === newNoteId)) {
			console.log(`[笔记列表] ⚠️ 笔记 ${newNoteId} 已存在，跳过添加`);
			return content;
		}
	}

	console.log('[笔记列表] 🔄 重新格式化笔记列表');
	// 格式化现有笔记列表
	let formattedNotesList = existingNotes.map((note, index) => {
		const noteContent = notesList.split('\n').find(line => line.includes(`[[${note.id}]]`));
		return `${index + 1}. ${noteContent?.trim()}`;
	}).join('\n');

	// 添加新笔记
	formattedNotesList += formattedNotesList ? `\n${existingNotes.length + 1}. ${noteLink}` : `1. ${noteLink}`;
	console.log(`[笔记列表] ✅ 成功添加新笔记，当前共 ${existingNotes.length + 1} 个笔记`);

	return `${header.trim()}\n\n## 笔记列表\n\n${formattedNotesList}`;
}

// 获取笔记标题函数
async function getNoteTitle(noteId: string, app: App): Promise<string> {
	const focusPath = '03-仓库/01-笔记/01-聚焦';
	const files = app.vault.getMarkdownFiles();

	const noteFile = files.find(file =>
		file.path.startsWith(focusPath) && file.basename.startsWith(noteId)
	);

	if (noteFile) {
		// 直接返回完整的 basename
		return noteFile.basename;
	}

	return noteId; // 如果找不到文件，返回原始ID
}

// 获取笔记内容函数
async function getNoteContent(noteId: string, app: App): Promise<{ title: string; summary: string }> {
	try {
		// 在 03-仓库/01-笔记/01-聚焦 目录下查找笔记
		const focusPath = '03-仓库/01-笔记/01-聚焦';
		const files = app.vault.getMarkdownFiles();
		
		const noteFile = files.find(file =>
			file.path.startsWith(focusPath) && file.basename.startsWith(noteId)
		);
		
		if (!noteFile) {
			throw new Error(`未找到笔记: ${noteId}`);
		}
		
		// 读取笔记内容
		const content = await app.vault.read(noteFile);
		
		// 获取摘要
		const summary = extractSummary(content);
		
		return {
			title: noteFile.basename,
			summary: summary || '无摘要'
		};
	} catch (error) {
		console.error(`获取笔记内容失败: ${noteId}`, error);
		throw error;
	}
}

// 更新主题函数
async function updateTopic(path: string, noteId: string, summary: string, app: App): Promise<void> {
	try {
		const file = app.vault.getAbstractFileByPath(path);
		if (!(file instanceof TFile)) {
			console.error(`[主题更新] ❌ 未找到文件: ${path}`);
			return;
		}

		const content = await app.vault.read(file);
		console.log(`[主题更新] 📖 正在处理主题文件: ${path}`);
		
		// 检查笔记是否已存在
		if (checkNoteExistence(content, noteId)) {
			console.log(`[主题更新] ⚠️ 笔记 ${noteId} 已存在于主题文件中，跳过添加`);
			return;
		}

		// 获取现有笔记数量
		const existingNotes = extractExistingNoteLinks(content);
		console.log(`[主题更新] 📊 当前主题包含 ${existingNotes.length} 个笔记`);

		// 构建新的笔记链接
		const noteTitle = await getNoteTitle(noteId, app);
		const noteLink = noteTitle ? 
			`[[${noteId}|${noteTitle}]]｜${summary}` :
			`[[${noteId}]]｜${summary}`;
		console.log(`[主题更新] ✏️ 准备添加新笔记: ${noteTitle || noteId}`);

		// 添加到笔记列表并格式化
		const updatedContent = appendToNotesList(content, noteLink);
		
		// 保存更新后的内容
		await app.vault.modify(file, updatedContent);
		console.log(`[主题更新] ✅ 成功更新主题文件: ${path}`);
		console.log(`[主题更新] 📝 新增笔记: ${noteTitle || noteId}`);
	} catch (error) {
		console.error(`[主题更新] ❌ 更新主题文件失败: ${path}`, error);
	}
}

// 创建或更新主题文件函数
async function createOrUpdateTopicFile(topicPath: string, noteId: string, noteSummary: string, app: App): Promise<void> {
	try {
		// 1. 解析路径和主题名称
		const isNewTopic = topicPath.startsWith('NEW:');
		const cleanPath = isNewTopic ? topicPath.substring(4).trim() : topicPath;
		
		// 2. 确定基础路径
		let basePath = '';
		if (cleanPath.startsWith('02-Areas/')) {
			basePath = '01-构建/02-Areas';
			console.info(`[主题创建] 📁 Areas路径: ${basePath}`);
		} else if (cleanPath.startsWith('03-Resources/')) {
			basePath = '01-构建/03-Resources';
			console.info(`[主题创建] 📁 Resources路径: ${basePath}`);
		} else {
			console.error(`[主题创建] ❌ 无效的主题路径格式: ${topicPath}`);
			return;
		}

		// 3. 提取主题名称并构建完整路径
		const topicName = cleanPath.split('/')[1];
		const fullPath = `${basePath}/${topicName}.md`;
		console.info(`[主题创建] 📝 完整路径: ${fullPath}`);

		// 4. 获取笔记内容
		const noteContent = await getNoteContent(noteId, app);
		const noteLink = `[[${noteContent.title}]]｜${noteContent.summary}`;
		console.info(`[主题创建] 📄 准备添加笔记: ${noteContent.title}`);

		// 5. 检查文件是否存在
		const file = app.vault.getAbstractFileByPath(fullPath);
		
		if (!file) {
			// 5.1 创建新文件
			console.info(`[主题创建] ✨ 创建新主题文件: ${fullPath}`);
			const initialContent = `## 笔记列表\n\n1. ${noteLink}`;
			await app.vault.create(fullPath, initialContent);
			console.info(`[主题创建] ✅ 成功创建主题文件`);
		} else if (file instanceof TFile) {
			// 5.2 更新现有文件
			console.info(`[主题创建] 📝 更新现有主题文件: ${fullPath}`);
			const content = await app.vault.read(file);
			const updatedContent = appendToNotesList(content, noteLink);
			await app.vault.modify(file, updatedContent);
			console.info(`[主题创建] ✅ 成功更新主题文件`);
		}
	} catch (error) {
		console.error(`[主题创建] ❌ 创建或更新主题文件失败:`, error);
	}
}

// 重新排序主题编号函数
async function reorderTopicNumbers(topicPaths: string[], app: App): Promise<string[]> {
	// 按照路径类型（Areas/Resources）分组
	const groupedPaths: { [key: string]: string[] } = {
		'Areas': [],
		'Resources': []
	};

	// 分组并提取编号和标题
	topicPaths.forEach(path => {
		const isAreas = path.includes('02-Areas');
		const group = isAreas ? 'Areas' : 'Resources';
		groupedPaths[group].push(path);
	});

	// 处理每个分组
	const processGroup = async (paths: string[]): Promise<string[]> => {
		const items: TopicItem[] = paths.map(path => {
			const match = path.match(/(\d+)\s+-\s+([^/]+)\.md$/);
			if (!match) {
				return {
					path: path,
					number: 99,
					title: 'Unknown',
					originalPath: path
				};
			}
			return {
				path: path,
				number: parseInt(match[1]),
				title: match[2],
				originalPath: path
			};
		});

		// 按编号排序
		items.sort((a, b) => a.number - b.number);

		// 重新分配编号并重命名文件
		const renamedPaths: string[] = [];
		for (let i = 0; i < items.length; i++) {
			const item = items[i];
			const newNumber = String(i + 1).padStart(2, '0');
			const pathParts = item.path.split('/');
			pathParts[pathParts.length - 1] = `${newNumber} - ${item.title}.md`;
			const newPath = pathParts.join('/');

			if (newPath !== item.path) {
				try {
					const file = app.vault.getAbstractFileByPath(item.originalPath);
					if (file instanceof TFile) {
						await app.fileManager.renameFile(file, newPath);
						console.info(`已重命名: ${item.originalPath} -> ${newPath}`);
					}
				} catch (error) {
					console.error(`重命名失败: ${item.originalPath}`, error);
				}
			}
			renamedPaths.push(newPath);
		}

		return renamedPaths;
	};

	// 处理每个分组并合并结果
	console.info('=== 主题笔记重排序 ===');
	const reorderedAreas = await processGroup(groupedPaths['Areas']);
	const reorderedResources = await processGroup(groupedPaths['Resources']);

	console.info('Areas主题数量:', reorderedAreas.length);
	console.info('Resources主题数量:', reorderedResources.length);

	return [...reorderedAreas, ...reorderedResources];
}

// 处理分类结果函数
async function processClassifications(
	classifications: Array<{note_id: string, paths: string[]}>, 
	app: App
): Promise<void> {
	const total = classifications.length;
	let processed = 0;

	for (const classification of classifications) {
		try {
			processed++;
			console.info(`处理进度: ${processed}/${total}`);

			const { note_id, paths } = classification;
			console.info(`处理笔记 ${note_id} 的分类，共 ${paths.length} 个主题`);

			// 查找笔记文件
			const noteFile = app.vault.getMarkdownFiles().find(file => {
				const isInFocusFolder = file.path.startsWith('03-仓库/01-笔记/01-聚焦');
				const isMatch = file.basename.startsWith(note_id);

				if (isInFocusFolder && isMatch) {
					console.info(`找到匹配的笔记: ${file.path}`);
					return true;
				}
				return false;
			});

			if (!noteFile) {
				console.warn(`未找到笔记: ${note_id}，在聚焦文件夹中的文件:`,
					app.vault.getMarkdownFiles()
						.filter(f => f.path.startsWith('03-仓库/01-笔记/01-聚焦'))
						.map(f => f.path)
				);
				continue;
			}

			// 读取笔记内容以获取摘要
			const content = await app.vault.read(noteFile);
			const summary = extractSummary(content);

			// 记录是否所有主题更新都成功
			let allTopicsUpdated = true;
			let successfulTopics: string[] = [];

			// 处理每个分类路径
			for (const path of paths) {
				try {
					// 确保路径格式正确
					let fullPath = path;
					if (!path.startsWith('01-构建/')) {
						fullPath = path.startsWith('02-Areas/') ?
							`01-构建/${path}` :
							path.startsWith('03-Resources/') ?
								`01-构建/${path}` : path;
					}

					console.info('Checking topic path:', fullPath);

					// 检查文件是否存在
					const file = app.vault.getAbstractFileByPath(fullPath);
					if (!file || !(file instanceof TFile)) {
						// 如果是新主题，创建它
						if (path.startsWith('NEW:')) {
							const newTopicName = path.replace('NEW:', '').trim();
							await createOrUpdateTopicFile(path, note_id, summary, app);
							successfulTopics.push(newTopicName);
						} else {
							// 如果不是新主题但文件不存在，尝试创建
							const topicName = path.split('/').pop() || '';
							await createOrUpdateTopicFile(path, note_id, summary, app);
							successfulTopics.push(topicName);
						}
					} else {
						// 更新现有主题
						await updateTopic(fullPath, note_id, summary, app);
						successfulTopics.push(path.split('/').pop() || '');
					}
				} catch (error) {
					console.error(`处理分类路径失败: ${path}`, error);
					new Notice(`⚠️ 处理分类路径失败: ${path}`);
					allTopicsUpdated = false;
				}
			}

			// 只有当所有主题都更新成功时，才移动笔记到存档
			if (allTopicsUpdated) {
				try {
					const archivePath = '03-仓库/01-笔记/02-存档';
					const targetPath = `${archivePath}/${noteFile.name}`;

					// 移动文件
					await app.fileManager.renameFile(noteFile, targetPath);
					console.info(`已将笔记移动到存档: ${noteFile.name}`);
					new Notice(`✨ 已处理并归档笔记: ${note_id}，添加到以下主题：${successfulTopics.join(', ')}`);
				} catch (moveError) {
					console.error(`移动笔记到存档失败: ${note_id}`, moveError);
					new Notice(`⚠️ 笔记已分类但移动失败: ${note_id}`);
				}
			} else {
				new Notice(`⚠️ 由于部分主题更新失败，笔记 ${note_id} 未移动到存档`);
			}

		} catch (error) {
			console.error(`处理笔记分类失败: ${classification.note_id}`, error);
			new Notice(`❌ 处理笔记失败: ${classification.note_id}`);
		}
	}
}

// API调用函数
async function callGeminiAPI(input: any, settings: NoteClassificationSettings): Promise<ClassificationResponse | null> {
	try {
		console.info('=== 准备调用 Gemini API ===');
		console.info('API Endpoint:', settings.apiEndpoint);
		console.info('输入数据:', JSON.stringify(input, null, 2));

		const requestBody = {
			contents: [{
				parts: [{
					text: `${NOTE_CLASSIFICATION_PROMPT}\n\nInput:\n${JSON.stringify(input, null, 2)}`
				}]
			}],
			generationConfig: {
				temperature: 0.7,
				topK: 40,
				topP: 0.95,
				maxOutputTokens: 8192,
				response_mime_type: "application/json"
			}
		};

		console.info('请求体:', JSON.stringify(requestBody, null, 2));

		const response = await fetch(`${settings.apiEndpoint}?key=${settings.apiKey}`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify(requestBody)
		});

		if (!response.ok) {
			throw new Error(`API 请求失败: ${response.statusText}`);
		}

		const data = await response.json();
		console.info('API 原始响应:', JSON.stringify(data, null, 2));

		const text = data.candidates[0].content.parts[0].text;
		console.info('提取的文本内容:', text);

		try {
			// 尝试直接解析整个响应
			console.info('尝试直接解析响应...');
			const result = JSON.parse(text);
			if (isValidClassificationResponse(result)) {
				console.info('成功解析为有效的分类响应');
				return result;
			}
			console.warn('解析结果不符合预期格式');
		} catch (parseError) {
			console.warn('直接解析失败，尝试提取 JSON 部分...', parseError);
			// 如果直接解析失败，尝试提取 JSON 部分
			const jsonMatch = text.match(/\{[\s\S]*\}/);
			if (!jsonMatch) {
				console.error('无法从响应中提取有效的 JSON');
				return null;
			}

			console.info('提取的 JSON 字符串:', jsonMatch[0]);

			try {
				const result = JSON.parse(jsonMatch[0]);
				if (isValidClassificationResponse(result)) {
					console.info('成功解析提取的 JSON 为有效的分类响应');
					return result;
				}
				console.warn('提取的 JSON 不符合预期格式');
			} catch (extractError) {
				console.error('提取的 JSON 格式无效:', extractError);
				return null;
			}
		}

		console.error('API 返回的数据格式不符合预期');
		return null;
	} catch (error) {
		console.error('调用分类 API 失败:', error);
		return null;
	}
}

// 主要功能函数
export async function handleNoteClassification(options: NoteClassificationOptions): Promise<void> {
	const { app, settings } = options;
	
	try {
		// 1. 获取聚焦文件夹中的所有笔记
		const focusPath = '03-仓库/01-笔记/01-聚焦';  // 使用相对路径
		const areasPath = '01-构建/02-Areas';         // 使用相对路径
		const resourcesPath = '01-构建/03-Resources';  // 使用相对路径

		console.info('=== 开始获取笔记文件 ===');
		console.info('相对路径 - 聚焦文件夹:', focusPath);
		console.info('相对路径 - Areas:', areasPath);
		console.info('相对路径 - Resources:', resourcesPath);

		// 获取所有笔记文件
		const allFiles = app.vault.getMarkdownFiles();
		console.info('vault中的所有Markdown文件数量:', allFiles.length);

		// 2. 预处理主题笔记，进行去重
		console.info('=== 开始预处理主题笔记 ===');
		const topicFiles = allFiles.filter(file => 
			file.path.startsWith(areasPath) || 
			file.path.startsWith(resourcesPath)
		);

		for (const topicFile of topicFiles) {
			try {
				console.info(`[预处理] 📝 处理主题文件: ${topicFile.path}`);
				const content = await app.vault.read(topicFile);
				
				// 提取并去重现有笔记
				const existingNotes = extractExistingNoteLinks(content);
				if (existingNotes.length > 0) {
					console.info(`[预处理] 📊 主题文件包含 ${existingNotes.length} 个笔记`);
					
					// 重新格式化笔记列表
					let formattedContent = content.split('## 笔记列表')[0].trim();
					const notesList = existingNotes.map((note, index) => 
						`${index + 1}. [[${note.id}]]｜${note.summary}`
					).join('\n');
					
					const updatedContent = `${formattedContent}\n\n## 笔记列表\n\n${notesList}`;
					
					// 更新文件内容
					await app.vault.modify(topicFile, updatedContent);
					console.info(`[预处理] ✅ 成功更新主题文件`);
				}
			} catch (error) {
				console.error(`[预处理] ❌ 处理主题文件失败: ${topicFile.path}`, error);
			}
		}

		// 3. 获取并重排序主题文件
		console.info('=== 获取主题文件 ===');
		const existingTopics = allFiles.filter(file => 
			file.path.startsWith(areasPath) || 
			file.path.startsWith(resourcesPath)
		);

		console.info('主题文件数量:', existingTopics.length);
		console.info('原始主题文件:', existingTopics.map(f => f.path));

		// 重新排序主题笔记的编号
		const reorderedTopicPaths = await reorderTopicNumbers(existingTopics.map(f => f.path), app);
		
		// 等待文件系统操作完成
		await new Promise(resolve => setTimeout(resolve, 500));

		// 重新获取更新后的主题文件列表
		const updatedTopics = app.vault.getMarkdownFiles()
			.filter(file => 
				file.path.startsWith(areasPath) || 
				file.path.startsWith(resourcesPath)
			)
			.map(file => file.path);

		console.info('=== 重排序后的主题文件 ===');
		console.info('更新后的主题文件:', updatedTopics);

		// 4. 提取笔记信息
		const files = allFiles.filter(file => file.path.startsWith(focusPath));
		console.info('=== 过滤后的聚焦笔记 ===');
		console.info('符合条件的笔记数量:', files.length);
		console.info('符合条件的笔记:', files.map(file => ({
			path: file.path,
			name: file.name
		})));

		if (files.length === 0) {
			new Notice('没有找到需要分类的笔记');
			return;
		}

		const noteInfos = await Promise.all(files.map(async file => {
			const content = await app.vault.read(file);
			const summary = extractSummary(content);
			const match = file.name.match(/(\d+)｜(.+)\.md$/);
			if (!match) {
				console.warn('笔记命名格式不正确:', file.name);
				return null;
			}
			return {
				id: match[1],
				title: match[2],
				summary: summary
			};
		}));

		// 过滤掉无效的笔记信息
		const validNoteInfos = noteInfos
			.filter((note): note is NonNullable<typeof note> => note !== null)
			.sort((a, b) => a.id.localeCompare(b.id)); // 按时间戳升序排序

		if (validNoteInfos.length === 0) {
			new Notice('没有找到有效的笔记');
			return;
		}

		// 5. 分批处理笔记（每批最多10个）
		const batchSize = 10;
		const batches = [];
		for (let i = 0; i < validNoteInfos.length; i += batchSize) {
			batches.push(validNoteInfos.slice(i, i + batchSize));
		}

		console.info(`=== 待处理笔记信息 ===`);
		console.info(`总笔记数: ${validNoteInfos.length}`);
		console.info(`分批数量: ${batches.length}`);

		// 只处理第一批
		const firstBatch = batches[0];
		console.info(`\n准备处理第一批笔记（${firstBatch.length}个）`);
		console.info(`本批次笔记:`, firstBatch);

		const input = {
			notes: firstBatch,
			topics: updatedTopics  // 使用更新后的主题路径
		};

		new Notice(`正在处理 ${firstBatch.length} 个笔记...`);
		const result = await callGeminiAPI(input, settings);
		
		if (!result) {
			console.error('处理失败');
			new Notice('❌ 笔记处理失败');
			return;
		}

		console.info('分类结果:', result);

		// 6. 处理分类结果
		if (result.classifications.length > 0) {
			console.info('=== 分类结果 ===');
			console.info('分类数:', result.classifications.length);
			console.info('分类详情:', result.classifications);

			await processClassifications(result.classifications, app);
			
			// 显示剩余笔记数量
			const remainingNotes = validNoteInfos.length - batchSize;
			if (remainingNotes > 0) {
				new Notice(`✨ 本批次处理完成！还有 ${remainingNotes} 个笔记待处理`);
			} else {
				new Notice('✨ 所有笔记都已处理完成！');
			}
		} else {
			new Notice('没有生成分类结果');
		}
	} catch (error) {
		console.error('笔记分类处理失败:', error);
		new Notice('❌ 笔记分类失败');
	}
}
