import { Editor, Notice } from 'obsidian';

// 提示词常量
export const CREATE_OUTLINE_PROMPT = `
你是一位专业的文档结构分析师和信息架构师。你的任务是根据用户提供的一系列内容块，生成一个结构清晰、层级分明的 Markdown 大纲。

# 目标
为用户提供一个易于导航的笔记内容索引，通过关键词驱动的标题快速定位信息。

# 输入格式
你将收到一个 JSON 数组，其中每个对象代表一个内容块，格式如下：
\`\`\`json
[
  {"1": "<内容块预览1>"},
  {"2": "<内容块预览2>"},
  ...
]
\`\`\`

内容块可能包含以下类型的标记：
- [代码块] - 表示这是一个代码块
- [表格] - 表示这是一个表格
- [列表：共N项] - 表示这是一个包含N个项目的列表
- [段落：共N行] - 表示这是一个包含N行的长段落

# 任务要求
1. 仔细分析所有内容块的预览信息，理解文档的整体结构和主题。
2. 识别文档中的主要议题、子议题以及它们之间的层级关系。
3. 为每个识别出的议题层级生成一个简洁的、**基于内容关键词**的 Markdown 标题。
4. 如果内容块中已经包含了特定标题，那么下方的标题都需要低一级，比如用户提供的内容块有一个一级标题，那么你提供的标题大纲，在这个内容层级下，最高只能使用二级标题
4. **严格按照以下 JSON 格式返回结果**：

\`\`\`json
{
  "outline": [
    "# 第一级标题",  // 字符串表示 Markdown 标题
    [1, 2, 3],     // 数组表示该标题下应包含的内容块编号（1-based）
    "## 第二级标题",
    [4, 5],
    "### 第三级标题",
    [6, 7, 8],
    "#### 第四级标题",
    [9]
    // ...可以有更多的标题和内容块引用
  ]
}
\`\`\`

# 重要规则
1. **使用关键词创建标题**：标题应该简洁，仅使用对内容描述性强的关键词，避免使用长句子或完整陈述。
2. **层级结构**：最多使用4级标题（# 到 ####），根据内容的层级关系合理组织。
3. **相关性分组**：将相关的内容块放在同一标题下，保持逻辑连贯性。
4. **输出格式**：必须严格遵循指定的JSON格式，数组中交替包含标题字符串和内容块引用数组。
5. **数据完整性**：确保每个内容块都被引用至少一次，不要遗漏任何内容。
6. !Important **仅生成中文标题**：不要生成其他语言的标题

内容块JSON数据：
{CONTENT_BLOCKS_JSON}
`;

// 类型定义
interface OutlineSettings {
	apiEndpoint: string;
	apiKey: string;
}

interface NoteOutlineOptions {
	editor: Editor;
	settings: OutlineSettings;
}

// 辅助函数：检查是否是列表项
function isListItem(line: string): boolean {
	return line.trim().match(/^[-*+]|\d+\.\s/) !== null;
}

// 辅助函数：获取行的缩进级别
function getIndentLevel(line: string): number {
	const match = line.match(/^(\s*)/);
	return match ? match[1].length : 0;
}

// 处理缩进的内部函数
function processIndentation(lines: string[]): string[] {
	if (lines.length === 0) return lines;
	
	// 找到列表的起始缩进级别
	let baseIndent = getIndentLevel(lines[0]);
	let isFirstItemList = isListItem(lines[0]);
	
	// 处理每一行的缩进
	return lines.map((line, index) => {
		if (line.trim() === '') return line;
		
		const currentIndent = getIndentLevel(line);
		const isCurrentList = isListItem(line);
		
		// 对于列表项，保持相对缩进
		if (isCurrentList) {
			const relativeIndent = Math.max(0, currentIndent - baseIndent);
			return ' '.repeat(relativeIndent) + line.trim();
		}
		
		// 对于非列表项，如果是在列表中，添加适当的缩进
		if (isFirstItemList && index > 0) {
			return '  ' + line.trim(); // 2个空格的缩进
		}
		
		return line.trim();
	});
}

// 分割内容块的内部函数
function splitIntoContentBlocks(text: string): string[] {
	const lines = text.split('\n');
	const blocks: string[] = [];
	
	let currentBlock: string[] = [];
	let inCodeBlock = false;
	let inTable = false;
	
	const addEmptyLines = (count: number, arr: string[]) => {
		for (let i = 0; i < count; i++) {
			arr.push('');
		}
	};
	
	for (let i = 0; i < lines.length; i++) {
		const line = lines[i];
		const trimmedLine = line.trim();
		
		// 检测代码块边界
		if (trimmedLine.startsWith('```')) {
			if (inCodeBlock) {
				// 代码块结束
				currentBlock.push(line);
				if (currentBlock.length > 0) {
					blocks.push(currentBlock.join('\n'));
					currentBlock = [];
				}
				inCodeBlock = false;
			} else {
				// 代码块开始
				if (currentBlock.length > 0) {
					blocks.push(currentBlock.join('\n'));
					currentBlock = [];
				}
				currentBlock.push(line);
				inCodeBlock = true;
			}
			continue;
		}
		
		// 在代码块中，直接添加行
		if (inCodeBlock) {
			currentBlock.push(line);
			continue;
		}
		
		// 检测表格
		if (trimmedLine.includes('|') && !inTable) {
			// 表格可能开始
			if (currentBlock.length > 0) {
				blocks.push(currentBlock.join('\n'));
				currentBlock = [];
			}
			currentBlock.push(line);
			inTable = true;
			continue;
		}
		
		if (inTable) {
			if (trimmedLine.includes('|')) {
				currentBlock.push(line);
				continue;
			} else {
				// 表格结束
				if (currentBlock.length > 0) {
					blocks.push(currentBlock.join('\n'));
					currentBlock = [];
				}
				inTable = false;
			}
		}
		
		// 标题检测
		if (trimmedLine.match(/^#{1,6}\s+/)) {
			if (currentBlock.length > 0) {
				blocks.push(currentBlock.join('\n'));
				currentBlock = [];
			}
			currentBlock.push(line);
			if (currentBlock.length > 0) {
				blocks.push(currentBlock.join('\n'));
				currentBlock = [];
			}
			continue;
		}
		
		// 空行处理
		if (trimmedLine === '') {
			if (currentBlock.length > 0) {
				// 如果当前块不为空，则将空行作为分隔符处理
				blocks.push(currentBlock.join('\n'));
				currentBlock = [];
			}
			continue;
		}
		
		// 普通行
		currentBlock.push(line);
	}
	
	// 处理最后一个块
	if (currentBlock.length > 0) {
		blocks.push(currentBlock.join('\n'));
	}
	
	// 过滤掉完全空白的块
	return blocks.filter(block => block.trim() !== '');
}

// API调用函数
async function callCreateOutlineAPI(
	contentBlocks: { [blockNumber: string]: string }[], 
	settings: OutlineSettings
): Promise<any> {
	try {
		// 将内容块数组转换为JSON字符串
		const contentBlocksJson = JSON.stringify(contentBlocks);
		
		// 使用Gemini API（与插件中其他功能使用相同的API）
		const response = await fetch(`${settings.apiEndpoint}?key=${settings.apiKey}`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				contents: [{
					parts: [
						{ text: CREATE_OUTLINE_PROMPT.replace('{CONTENT_BLOCKS_JSON}', contentBlocksJson) }
					]
				}],
				generationConfig: {
					temperature: 0.2,
					topK: 32,
					topP: 0.95,
					maxOutputTokens: 8192,
					response_mime_type: "application/json"
				}
			})
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error('API调用失败:', errorText);
			throw new Error(`API调用失败: ${response.status} ${response.statusText}`);
		}

		const data = await response.json();
		console.log('API返回结果:', data);

		// 解析Gemini的回复
		if (!data.candidates?.[0]?.content?.parts?.[0]?.text) {
			throw new Error('API返回了无效响应');
		}

		const content = data.candidates[0].content.parts[0].text;
		
		// 尝试提取JSON部分
		const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
		if (jsonMatch) {
			try {
				return JSON.parse(jsonMatch[1].trim());
			} catch (e) {
				console.error('解析JSON失败:', e);
				throw new Error('解析AI返回的JSON失败');
			}
		} else {
			try {
				// 尝试直接解析内容
				return JSON.parse(content.trim());
			} catch (e) {
				console.error('直接解析内容失败:', e);
				throw new Error('无法从API响应中解析JSON');
			}
		}
	} catch (error) {
		console.error('调用创建大纲API时出错:', error);
		new Notice(`调用创建大纲API失败: ${error.message}`);
		throw error;
	}
}

// 主要功能函数
export async function handleCreateNoteOutline(options: NoteOutlineOptions): Promise<void> {
	const { editor, settings } = options;
	
	try {
		const selection = editor.getSelection();
		if (!selection) {
			new Notice('请先选择文本内容');
			return;
		}

		// 将文本分割为内容块（不是按行分割）
		const contentBlocks = splitIntoContentBlocks(selection);
		console.log(`INFO - [main.ts][handleCreateNoteOutline_001] 识别到 ${contentBlocks.length} 个内容块`);
		
		// 将内容块转换为API需要的格式
		const blockObjectsForAI = contentBlocks.map((block, index) => {
			// 返回只包含内容块预览（第一行或摘要）的对象
			return { [index + 1]: block };
		});

		// 调用 AI API
		new Notice(`正在分析 ${contentBlocks.length} 个内容块并生成大纲...`);
		const aiResponse = await callCreateOutlineAPI(blockObjectsForAI, settings);
		
		// 检查 AI 响应是否有效
		if (!aiResponse || !Array.isArray(aiResponse.outline)) {
			new Notice('❌ 创建大纲失败：AI 未返回有效数据。');
			console.error('AI response for outline creation was invalid:', aiResponse);
			return;
		}

		// 解析 AI 响应并重建 Markdown 大纲
		let markdownOutline = "";
		try {
			for (const item of aiResponse.outline) {
				if (typeof item === 'string') {
					// 标题项直接添加
					markdownOutline += item + "\n";
				} else if (Array.isArray(item)) {
					// 内容块引用数组
					if (item.length > 0) {
						// 合并同类型连续内容块
						let mergedBlocks: { lines: string[], blockNumbers: number[] }[] = [];
						
						// 分组处理内容块
						for (const blockNumber of item) {
							// blockNumber is 1-based from AI
							if (blockNumber > 0 && blockNumber <= contentBlocks.length) {
								// 获取当前内容块并分割为行
								const lines = contentBlocks[blockNumber - 1].split('\n');
								
								// 跳过空内容块
								if (lines.length === 0 || (lines.length === 1 && lines[0].trim() === '')) {
									continue;
								}
								
								// 检查当前块的类型（是否包含列表项）
								const containsList = lines.some(line => isListItem(line));
								
								// 检查是否应该合并到前一个块
								const shouldMergeWithPrevious = mergedBlocks.length > 0 && 
									((containsList && mergedBlocks[mergedBlocks.length - 1].lines.some(line => isListItem(line))) ||
									(!containsList && !mergedBlocks[mergedBlocks.length - 1].lines.some(line => isListItem(line))));
								
								if (shouldMergeWithPrevious) {
									// 合并到前一个块
									mergedBlocks[mergedBlocks.length - 1].lines.push(...lines);
									mergedBlocks[mergedBlocks.length - 1].blockNumbers.push(blockNumber);
								} else {
									// 创建新的合并块
									mergedBlocks.push({
										lines: [...lines],
										blockNumbers: [blockNumber]
									});
								}
							}
						}
						
						// 输出合并后的内容块
						for (const block of mergedBlocks) {
							// 处理缩进问题
							const processedLines = processIndentation(block.lines);
							markdownOutline += processedLines.join('\n') + "\n";
						}
					}
				}
			}
			markdownOutline = markdownOutline.trim(); // Remove trailing newlines
		} catch (error) {
			new Notice('❌ 创建大纲失败：解析AI响应时出错。');
			console.error('Error reconstructing outline from AI response:', error);
			return;
		}

		// 在大纲前后添加分割线
		const finalOutline = `---\n${markdownOutline}\n---`;
		
		// 使用重建的 Markdown 大纲替换选中的文本
		editor.replaceSelection(finalOutline);
		new Notice(`✨ 笔记大纲创建成功！处理了 ${contentBlocks.length} 个内容块`);
	} catch (error) {
		console.error('创建笔记大纲时出错:', error);
		new Notice('创建笔记大纲失败: ' + error.message);
	}
}



