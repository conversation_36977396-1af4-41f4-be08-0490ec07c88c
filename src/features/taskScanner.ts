import { App, TFile } from 'obsidian';
import { TaskItem, TaskScanResult, TaskType } from '../types';

/**
 * 任务扫描器 - 负责从指定文件中扫描带时间戳的任务
 */
export class TaskScanner {
    private app: App;

    constructor(app: App) {
        this.app = app;
    }

    /**
     * 扫描指定文件列表中的所有任务
     * @param filePaths 文件路径列表，用 | 分隔
     * @returns 扫描结果
     */
    async scanTasks(filePaths: string): Promise<TaskScanResult> {
        const result: TaskScanResult = {
            tasks: [],
            errors: []
        };

        if (!filePaths.trim()) {
            result.errors.push('未配置任务扫描文件');
            return result;
        }

        const paths = filePaths.split('|').map(path => path.trim()).filter(path => path);

        for (const path of paths) {
            try {
                const tasks = await this.scanFileForTasks(path);
                result.tasks.push(...tasks);
            } catch (error) {
                result.errors.push(`扫描文件 ${path} 时出错: ${error.message}`);
            }
        }

        return result;
    }

    /**
     * 扫描单个文件中的任务
     * @param filePath 文件路径
     * @returns 任务列表
     */
    private async scanFileForTasks(filePath: string): Promise<TaskItem[]> {
        const file = this.app.vault.getAbstractFileByPath(filePath);
        
        if (!file || !(file instanceof TFile)) {
            throw new Error(`文件不存在或不是有效的文件: ${filePath}`);
        }

        const content = await this.app.vault.read(file);
        const lines = content.split('\n');
        const tasks: TaskItem[] = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const taskMatch = this.parseTaskLine(line);
            
            if (taskMatch) {
                const indentedContent = this.extractIndentedContent(lines, i);
                
                const task: TaskItem = {
                    id: taskMatch.timestamp || this.generateTaskId(taskMatch.content, i),
                    content: taskMatch.content,
                    topic: taskMatch.topic || '日常',
                    completed: taskMatch.completed,
                    taskType: taskMatch.taskType,
                    sourceFile: filePath,
                    sourceLine: i + 1,
                    indentedContent,
                    originalLine: line
                };

                tasks.push(task);
            }
        }

        return tasks;
    }

    /**
     * 解析任务行
     * @param line 文本行
     * @returns 解析结果或 null
     */
    private parseTaskLine(line: string): {
        timestamp: string;
        topic: string;
        content: string;
        completed: boolean;
        taskType: TaskType;
    } | null {
        // 首先尝试匹配有时间戳的任务：[ ] 或 [x] 后跟时间戳｜主题｜内容
        const timestampTaskRegex = /^(\s*)(?:\d+\.\s*)?(?:-\s*)?\[([ x])\]\s*(\d{14})｜([^｜]*)｜(.*)$/;
        const timestampMatch = line.match(timestampTaskRegex);

        if (timestampMatch) {
            const [, , checkboxState, timestamp, topic, content] = timestampMatch;
            const completed = checkboxState.toLowerCase() === 'x';
            const taskType = this.determineTaskType(timestamp, completed);

            return {
                timestamp,
                topic: topic.trim(),
                content: content.trim(),
                completed,
                taskType
            };
        }

        // 然后尝试匹配普通任务：[ ] 或 [x] 后跟任务内容（没有时间戳）
        const normalTaskRegex = /^(\s*)(?:\d+\.\s*)?(?:-\s*)?\[([ x])\]\s*(.+)$/;
        const normalMatch = line.match(normalTaskRegex);

        if (normalMatch) {
            const [, , checkboxState, content] = normalMatch;
            const completed = checkboxState.toLowerCase() === 'x';

            return {
                timestamp: '', // 普通任务没有时间戳
                topic: '日常', // 默认主题
                content: content.trim(),
                completed,
                taskType: TaskType.NORMAL
            };
        }

        return null;
    }

    /**
     * 根据时间戳和完成状态确定任务类型
     * @param timestamp 时间戳
     * @param completed 是否完成
     * @returns 任务类型
     */
    private determineTaskType(timestamp: string, completed: boolean): TaskType {
        // 有时间戳的任务
        if (timestamp && this.isValidTimestamp(timestamp)) {
            return completed ? TaskType.COMPLETE : TaskType.PLANNED;
        }

        // 没有时间戳的任务
        return TaskType.NORMAL;
    }

    /**
     * 提取任务下方的缩进内容
     * @param lines 所有行
     * @param taskLineIndex 任务行索引
     * @returns 缩进内容数组
     */
    private extractIndentedContent(lines: string[], taskLineIndex: number): string[] {
        const indentedContent: string[] = [];
        const taskLine = lines[taskLineIndex];
        const taskIndentLevel = this.getIndentLevel(taskLine);

        // 从任务行的下一行开始检查
        for (let i = taskLineIndex + 1; i < lines.length; i++) {
            const line = lines[i];
            
            // 空行跳过
            if (line.trim() === '') {
                continue;
            }

            const lineIndentLevel = this.getIndentLevel(line);
            
            // 如果缩进级别小于等于任务行，说明缩进内容结束
            if (lineIndentLevel <= taskIndentLevel) {
                break;
            }

            indentedContent.push(line);
        }

        return indentedContent;
    }

    /**
     * 获取行的缩进级别
     * @param line 文本行
     * @returns 缩进级别（空格数）
     */
    private getIndentLevel(line: string): number {
        const match = line.match(/^(\s*)/);
        return match ? match[1].length : 0;
    }

    /**
     * 验证时间戳格式
     * @param timestamp 时间戳字符串
     * @returns 是否有效
     */
    private isValidTimestamp(timestamp: string): boolean {
        return /^\d{14}$/.test(timestamp);
    }

    /**
     * 为普通任务生成唯一ID
     * @param content 任务内容
     * @param lineIndex 行索引
     * @returns 生成的ID
     */
    private generateTaskId(content: string, lineIndex: number): string {
        // 为普通任务生成基于内容和行号的唯一ID
        const contentHash = content.substring(0, 10).replace(/[^a-zA-Z0-9]/g, '');
        return `normal_${lineIndex}_${contentHash}`;
    }
}
