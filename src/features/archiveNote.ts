import { TFile, Notice, App } from 'obsidian';

// 类型定义
interface ArchiveSettings {
	archivePath: string;
}

interface ArchiveNoteOptions {
	app: App;
	settings: ArchiveSettings;
	file: TFile;
}

// 移动文件到存档
export async function moveToArchive(options: ArchiveNoteOptions): Promise<void> {
	const { app, settings, file } = options;
	
	try {
		// 构建目标路径
		const targetPath = settings.archivePath;
		if (!targetPath) {
			new Notice('未配置存档路径');
			return;
		}

		// 确保目标文件夹存在
		const folderPath = targetPath.startsWith('/') ? targetPath.slice(1) : targetPath;
		if (!await app.vault.adapter.exists(folderPath)) {
			await app.vault.createFolder(folderPath);
		}

		// 构建新的文件路径
		const newPath = `${folderPath}/${file.name}`;

		// 移动文件
		await app.fileManager.renameFile(file, newPath);
		new Notice(`文件已移动到存档`);
	} catch (error) {
		console.error('移动文件到存档时出错:', error);
		new Notice('移动文件失败: ' + error.message);
	}
}
