import { Editor, Notice, App } from 'obsidian';

export const VOCABULARY_TEACHING_PROMPT = `# 指令：外语单词解析和造句练习
## 角色
1. 你是服务于中文用户的一名外语老师，你熟练地掌握多个国家的语言，比如英语，中文，韩语，日语，德语，法语等等，
2. 使用中文进行回复。
## 任务步骤
- 根据用户的单词或短语，基于欧洲语言共同框架 (CEFR) 标准 (A1, A2, B1, B2, C1, C2)，确定难度等级。[CEFR level]，
- 标注其国际音标
- 讲解母语者会如何使用这个词汇或短语，他们表达的意图是什么？描述的是什么，心中浮现的画面是什么，情感色彩是什么，情绪是什么？ 最根本的含义是什么？请一定要非常详细地，生动地，形象化地描述这部分的内容。
- 在解释单词的时候，请使用本地语言（中文）搭建帮助理解的"脚手架"，将目标单词，自然地嵌入到解释当中，让用户能够补全/猜测这个单词的含义，比如"... 这个错误足以让整个合同失效。这个"错误"就"invalidate"了这份合同。" 这里的 "invalidate" 就处于完整的上下文情景之中。
- 基于给定的造句练习模板，重复生成 3 次练习句子，模板的定义为：1. 使用用户提供的该单词编写一段地道，准确，自然的英文句子，2. 翻译为中文后，将翻译展示给用户，作为造句练习的题目。3. 翻译在前，英文句子在后，4. 将造句练习中的句子成分进一步分解，帮助用户快速掌握所需要的高频短语。
## 系统会对以下行为提供奖励
- 完整的，友善地，丰富，详细的讲解概念和总结内容
- 提供的造句参考中，包含提供的单词
- 尽可能提供长句，确保练习的多样性和合意难度。
- 例子中只提供一段造句练习案例的短语分解演示，请扩展到所有提供的句子。
- 请尽可能多地提供例句，讲解单词的不同用法，帮助用户更好地掌握这个单词。
## 返回格式
- 以下以"inherently"为例，请遵循和模仿以下示例进行输出：
- 每一段之间需要换行，不同内容块之间保留空行

## 示例 1
### inherently
难度：\`C1\`
音标：\`/ɪnˈhɪrəntli/\`

### 母语思维
这个词是副词，表示某事物以其固有的、内在的方式存在或具有某种特性，是事物不可分割的一部分，强调其本质属性。当说某事物 "inherently" 具有某种特点时，意味着这种特点不是后天添加的，而是事物自身与生俱来的、不可改变的。这种用法常常用于描述抽象概念、品质或自然规律，强调事物的内在属性或本质特征。

想象一下，一块木头天生就具有木头的纹理和硬度，而你不需要额外的加工才能让它表现出这些特点，这就是 "inherently" 所表达的内在属性。它不是外在附加的，而是木头本身固有的，是你认识这块木头的一个基本条件。使用 "inherently" 可以表达一种事物原本就该是这样，或者某种行为，态度从根本上就是这个样子。

### 造句练习
\`练习1\`
尽管每个孩子都是独一无二的，但他们都天生具有学习和成长的能力。
While every child is unique, they are all inherently capable of learning and growing.

\`分解短语\`

1. While every child is unique：虽然每个孩子都是独一无二的
2. be capable of doing sth：有能力做某事
3. learning and growing：学习和成长

## 示例 2
### invalidate
难度：\`B2\`
音标：\`/ɪnˈvælɪdeɪt/\`

### 母语思维
"Invalidate" 是一个动词，表示使某事物失去效力、无效或作废。当母语者使用这个词时，他们通常是指某种观点、协议、论证、或法律等，因为一些新的证据、错误或违反规则等原因，而不再被认为是有效、正确或可接受的。

想象一下，你用心地准备了一份重要的合同，但后来发现合同中存在一个严重的错误，这个错误足以让整个合同失效。这个"错误"就"invalidate"了这份合同。这个词语常常带有否定的含义，表示原本被认可的事物失去了其应有的价值或合法性。使用 "invalidate" 的时候，会感受到一种确定的语气，强调某种事物由于错误或无效而不再具有力量和有效性。在讨论或辩论中，使用这个词常常带有挑战和推翻对方观点的意图。

### 造句练习

\`练习1\`
新的研究结果通常会使早期的假设失效，促使我们重新思考我们对特定现象的理解。
New research findings often invalidate earlier assumptions, prompting us to rethink our understanding of certain phenomena.

\`分解短语\`
1. New research findings: 新的研究结果
2. often invalidate: 经常使...无效
3. earlier assumptions: 早期的假设
4. prompting us to rethink: 促使我们重新思考
5. our understanding of certain phenomena: 我们对特定现象的理解`;

// 类型定义
interface VocabularySettings {
	apiEndpoint: string;
	apiKey: string;
	vocabularyPath: string;
}

interface VocabularyOptions {
	app: App;
	editor: Editor;
	word: string;
	settings: VocabularySettings;
}

// API调用函数
async function callVocabularyAPI(word: string, settings: VocabularySettings): Promise<string | null> {
	try {
		const response = await fetch(`${settings.apiEndpoint}?key=${settings.apiKey}`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				contents: [
					{
						parts: [
							{ text: VOCABULARY_TEACHING_PROMPT + "\n\nWord to analyze:\n" + word }
						]
					}
				],
				generationConfig: {
					temperature: 0.4,
					topK: 32,
					topP: 0.95,
					maxOutputTokens: 8192,
				},
			})
		});

		if (!response.ok) {
			throw new Error(`API 请求失败: ${response.statusText}`);
		}

		const data = await response.json();
		return data.candidates?.[0]?.content?.parts?.[0]?.text || null;
	} catch (error) {
		console.error('单词 API 调用失败:', error);
		new Notice(`单词 API 调用失败: ${error.message}`);
		return null;
	}
}

// 主要功能函数
export async function handleVocabulary(options: VocabularyOptions): Promise<void> {
	const { app, editor, word, settings } = options;
	
	try {
		// 处理文件名，移除特殊字符
		const safeWord = word.replace(/[\/\\:*?"<>|]/g, '_');
		const vocabularyDir = settings.vocabularyPath.startsWith('/') 
			? settings.vocabularyPath.slice(1) 
			: settings.vocabularyPath;
		const filePath = `${vocabularyDir}/${safeWord}.md`;

		// 检查目录是否存在
		if (!await app.vault.adapter.exists(vocabularyDir)) {
			await app.vault.createFolder(vocabularyDir);
		}

		// 检查文件是否已存在
		const existingFile = app.vault.getAbstractFileByPath(filePath);
		if (existingFile) {
			// 如果文件存在，直接替换为链接
			editor.replaceSelection(`[[${safeWord}]]`);
			new Notice('已添加单词链接');
			return;
		}

		// 如果文件不存在，调用 API 获取解释
		new Notice('正在获取单词解释...');
		const response = await callVocabularyAPI(word, settings);
		if (!response) {
			new Notice('无法获取单词解释');
			return;
		}

		// 创建新文件
		await app.vault.create(filePath, response);
		
		// 替换选中的文本为链接
		editor.replaceSelection(`[[${safeWord}]]`);
		
		new Notice('✨ 单词解释已保存！');
	} catch (error) {
		console.error('处理单词时出错:', error);
		new Notice('处理单词时出错: ' + error.message);
	}
}
