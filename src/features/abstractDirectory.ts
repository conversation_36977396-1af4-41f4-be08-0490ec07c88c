import { Editor, TFile, Notice } from 'obsidian';

// 摘要目录常量
export const SUMMARY_PREFIX = "摘要：";
export const DIRECTORY_SEPARATOR = "\n---\n\n"; // 分隔符前后各有一个空行，确保格式
export const ABSTRACT_DIRECTORY_HEADER = "# 摘要目录";

// 类型定义
interface AbstractDirectoryOptions {
	editor: Editor;
	file: TFile;
}

// 主要功能函数
export async function generateAbstractDirectory(options: AbstractDirectoryOptions): Promise<void> {
	const { editor, file } = options;
	
	try {
		// 获取当前笔记内容
		const originalContent = editor.getValue();
		
		// 按行分割内容并提取摘要
		const lines = originalContent.split('\n');
		const abstracts: string[] = [];
		
		// 逐行识别，提取以 "摘要：" 开头的行
		for (const line of lines) {
			if (line.trim().startsWith(SUMMARY_PREFIX)) {
				abstracts.push(line.trim().substring(SUMMARY_PREFIX.length).trim());
			}
		}
		
		// 检查是否提取到摘要
		if (abstracts.length === 0) {
			new Notice(`未在笔记中找到以 "${SUMMARY_PREFIX}" 开头的摘要内容。`);
			return;
		}
		
		// 移除旧的摘要目录 (实现幂等性)
		let contentWithoutOldDirectory = originalContent;
		// 正则表达式匹配从 "# 摘要目录" 开始，到 "---" 分割线的部分
		const oldDirectoryRegex = new RegExp(`^${ABSTRACT_DIRECTORY_HEADER}\\s*[\\s\\S]*?${DIRECTORY_SEPARATOR.trim()}\\s*\\n?`, 'm');
		const matchOldDirectory = originalContent.match(oldDirectoryRegex);

		if (matchOldDirectory && matchOldDirectory[0]) {
			// 确保只移除匹配到的第一个旧目录部分
			const oldDirStartIndex = originalContent.indexOf(matchOldDirectory[0]);
			if (oldDirStartIndex === 0) { // 确保旧目录在文件开头
				contentWithoutOldDirectory = originalContent.substring(matchOldDirectory[0].length);
			} else if (originalContent.trim().startsWith(ABSTRACT_DIRECTORY_HEADER)) {
				// 处理一种特殊情况，即文件开头可能有少量空格或换行，但逻辑上仍是旧目录开头
				if (originalContent.startsWith(ABSTRACT_DIRECTORY_HEADER)) {
					const endOfOldDirIndex = originalContent.indexOf(DIRECTORY_SEPARATOR.trim());
					if (endOfOldDirIndex > -1) {
						contentWithoutOldDirectory = originalContent.substring(endOfOldDirIndex + DIRECTORY_SEPARATOR.trim().length);
						// 确保移除后的内容前有多余的换行符也一并清除
						contentWithoutOldDirectory = contentWithoutOldDirectory.replace(/^\s*\n/, '');
					}
				}
			}
		}
		
		// 构建新的摘要目录字符串
		let newAbstractDirectorySection = `${ABSTRACT_DIRECTORY_HEADER}\n\n`;
		abstracts.forEach(abstract => {
			newAbstractDirectorySection += `- ${abstract}\n`;
		});
		newAbstractDirectorySection += DIRECTORY_SEPARATOR;
		
		// 拼接最终的笔记内容
		const finalContent = newAbstractDirectorySection + contentWithoutOldDirectory;
		
		// 更新编辑器内容
		editor.setValue(finalContent);
		
		// 通知用户操作完成
		new Notice('摘要目录已生成！');
		
	} catch (error) {
		console.error('生成摘要目录时出错:', error);
		throw error;
	}
}
