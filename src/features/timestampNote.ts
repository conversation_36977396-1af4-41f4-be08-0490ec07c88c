import { Editor, Notice, TFile, App } from 'obsidian';

// 类型定义
interface TimestampNoteOptions {
	editor: Editor;
	app: App;
	selectedContent: string;
}

// 生成时间戳文件名 (包含秒)
function generateTimestampFileName(): string {
	const now = new Date();
	const year = now.getFullYear();
	const month = String(now.getMonth() + 1).padStart(2, '0');
	const day = String(now.getDate()).padStart(2, '0');
	const hour = String(now.getHours()).padStart(2, '0');
	const minute = String(now.getMinutes()).padStart(2, '0');
	const second = String(now.getSeconds()).padStart(2, '0'); // 添加秒

	return `${year}${month}${day}${hour}${minute}${second}`; // 包含秒
}

// 主要功能函数
export async function mergeToTimestampNote(options: TimestampNoteOptions): Promise<void> {
	const { editor, app, selectedContent } = options;

	try {
		// 生成时间戳文件名
		const timestamp = generateTimestampFileName();
		const fileName = `${timestamp}.md`;

		// 目标目录路径（相对于 vault 根目录）
		const targetDirPath = '03-仓库/01-笔记/01-聚焦';

		// 确保目标目录存在
		const targetDir = app.vault.getAbstractFileByPath(targetDirPath);
		if (!targetDir) {
			// 如果目录不存在，创建目录结构
			await app.vault.createFolder(targetDirPath);
		}

		// 创建新的时间戳笔记（每个时间戳都是唯一的）
		const fullFilePath = `${targetDirPath}/${fileName}`;
		const content = selectedContent.trim();
		const targetFile = await app.vault.create(fullFilePath, content);

		// 根据是否有内容调整提示信息
		if (selectedContent.trim() === '') {
			new Notice(`✅ 已创建时间戳笔记并链接: ${fileName}`);
		} else {
			new Notice(`✅ 已创建新的时间戳笔记: ${fileName}`);
		}

		// 在原编辑区域替换为链接
		const linkText = `[[${targetFile.basename}]]`;
		editor.replaceSelection(linkText);

		// 新增：在新的标签页中打开笔记但保持当前页面活跃
		await app.workspace.openLinkText(targetFile.path, '', 'tab', { active: false });

	} catch (error) {
		console.error('合并到时间戳笔记时出错:', error);
		new Notice('合并到时间戳笔记失败: ' + error.message);
	}
}
