import { Editor, Notice, TFile } from 'obsidian';

// 类型定义
interface StructuredKnowledgeSettings {
	apiEndpoint: string;
	apiKey: string;
}

interface StructuredKnowledgeOptions {
	editor: Editor;
	file: TFile;
	settings: StructuredKnowledgeSettings;
}

// 提示词
export const STRUCTURED_PROMPT = `
以清晰理性的写作风格，改写和润色输入内容，辅助构建逻辑清晰的知识系统。

具体而言，你需要：
1.  理解用户输入的内容，识别并为原文添加关键概念和核心问题，从而协助建立逻辑清晰的知识系统。
2.  使用完整、重点突出、逻辑有序的写作方式进行表达。
3.  通过加粗关键词、使用有序数字列表，并使用层级结构来展示知识体系，并逐步增加复杂性和叙述的详细程度，分块地呈现内容。
4.  在处理用户输入时，避免进行任何主观评价。

# Steps

1. **识别核心问题和关键概念：** 
   - 阅读用户输入，提取主要讨论点。
   - 确定输入文本试图解决的核心问题或疑问。
   - 找出支撑讨论的关键概念和术语。
2. **结构化内容：**
    -  将核心问题和关键概念整理为列表，并加粗关键词。
    - 将内容分层级展示，先呈现问题，再逐步深入。
    - 使用有序列表组织每个层级下的要点。
3. **润色和表达：**
   - 使用完整句子，确保重点突出。
   - 使用逻辑连接词，保持内容连贯。
   - 逐步增加内容的详细程度。
4. **避免主观评价：**
   - 仅做内容组织和结构化，不添加个人观点或评价。

# Output Format

输出为结构化的文本，包含以下部分：
1. **核心问题与概念**：使用列表形式，加粗关键词。
2. **主要内容**：使用有序列表和层级结构，逐步增加复杂性。
3. **总结**： 简洁总结润色后的内容。
4. 不要输出任何引导语，直接开始第一步，分析核心问题与概念

# 输出示例
**核心问题与概念**

*   **核心问题：** 当前向量数据库在个人私有部署场景下的应用价值，以及如何更好地发挥其在 AI 应用中的潜力。
*   **关键概念：**
    * **向量检索：**  基于向量相似度的信息检索方法，用于寻找语义相关的文档。
    * **意图搜索：**  根据用户或AI的潜在需求和目的进行信息检索，而非简单的关键词匹配。
    * **知识库：**  存储结构化或非结构化知识的仓库，可以是文档、笔记等。
    * **摘要：**  对原始文本的精简概述，用于快速理解内容。
    * **Agent：**  在特定环境中执行任务的自主智能体，例如大语言模型。
    * **聚焦层：**  在进行向量检索前，通过关键词过滤，缩小搜索范围。

**当前向量数据库的局限性**

1.  **自然语言检索的挑战：**
    * **跨语言检索问题：** 使用不同语言（如中文查询英文存储内容）会导致检索结果不准确。
    * **关键词依赖：** 必须预先知道要查询什么，以及包含哪些关键词才能有效检索，这限制了其探索性。
    * **意图不确定性：** 检索需求往往来自不稳定的意图，难以重复。

2.  **功能局限：**
    * **仅为检索工具：**  向量检索仅提供检索功能，缺乏 NotebookLM  的全局整合和提纲能力。
    * **局部应用：**  主要用途是根据关键词返回相关答案，应用场景较为局部。

....

**总结**

	* 向量数据库作为一种检索工具，其价值在不同的应用场景中有所差异。在个人私有部署和自动化内容生产方面，可能存在局限性。但当作为大语言模型的 Agent 时，向量数据库可以通过意图驱动的搜索机制，有效扩展 AI 的知识获取能力。
	* 为了解决大语言模型检索难题，可以通过关键词生成和过滤的方式，有效地匹配相关向量，再进行摘要和原文的检索，从而实现更高效的知识利用。
`;

// API调用函数
async function callStructureAPI(
	content: string,
	settings: StructuredKnowledgeSettings
): Promise<string | null> {
	try {
		const response = await fetch(`${settings.apiEndpoint}?key=${settings.apiKey}`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				contents: [{
					parts: [
						{ text: STRUCTURED_PROMPT + "\n\nText to analyze:\n" + content }
					]
				}],
				generationConfig: {
					temperature: 1,
					topK: 40,
					topP: 0.95,
					maxOutputTokens: 8192,
				}
			})
		});

		if (!response.ok) {
			throw new Error(`API 请求失败: ${response.statusText}`);
		}

		const data = await response.json();
		return data.candidates[0].content.parts[0].text;
	} catch (error) {
		console.error('结构化 API 调用失败:', error);
		return null;
	}
}

// 主要功能函数
export async function generateStructuredKnowledge(options: StructuredKnowledgeOptions): Promise<void> {
	const { editor, file, settings } = options;
	
	try {
		new Notice('开始生成知识结构...');
		const content = editor.getValue();
		
		// 调用 AI API
		new Notice('正在调用 AI API...');
		const response = await callStructureAPI(content, settings);
		if (!response) {
			new Notice('无法获取 AI 响应');
			return;
		}

		// 处理响应内容 - 使用分割线包裹，移除 Callout 结构
		const structuredContent = `---\n${response.trim()}\n---\n\n`;
		
		// 在第一个分隔符后插入结构化内容
		const currentContent = editor.getValue();
		const firstSeparatorIndex = currentContent.indexOf('---');
		
		if (firstSeparatorIndex !== -1) {
			// 找到分隔符后的第一个换行符
			const afterSeparator = currentContent.indexOf('\n', firstSeparatorIndex);
			if (afterSeparator !== -1) {
				// 在分隔符后插入结构化内容
				const newContent = 
					currentContent.slice(0, afterSeparator + 1) + 
					'\n' + // 额外添加一个空行
					structuredContent +
					currentContent.slice(afterSeparator + 1);
				editor.setValue(newContent);
			}
		} else {
			// 如果没有找到分隔符，则在开头插入
			editor.setValue(structuredContent + currentContent);
		}
		
		new Notice('✨ 知识结构生成完成！');
	} catch (error) {
		console.error('生成知识结构时出错:', error);
		new Notice('生成知识结构时出错: ' + error.message);
	}
}




