import { App, TFile, Notice } from 'obsidian';
import { RAContentItem, RAScanResult, TopicNoteInfo } from '../types';

/**
 * RA 内容同步管理器 - 负责识别和同步 Reasoning - Action 内容到专题笔记
 */
export class RAContentSyncManager {
    private app: App;
    private areasPath: string = '01-构建/05-专题笔记';

    constructor(app: App) {
        this.app = app;
    }

    /**
     * 从当前打开的笔记中扫描 RA 内容
     * @param content 笔记内容
     * @param filePath 文件路径
     * @returns RA 扫描结果
     */
    scanRAContent(content: string, filePath: string): RAScanResult {
        const lines = content.split('\n');
        const raItems: RAContentItem[] = [];
        const errors: string[] = [];

        // RA 标记的正则表达式：时间戳｜Reasoning - Action｜主题
        const raPattern = /^(\d{14})｜Reasoning\s*-\s*Action｜(.+)$/;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            const match = line.match(raPattern);

            if (match) {
                try {
                    const timestamp = match[1];
                    const topicsStr = match[2];
                    const topics = topicsStr.split(/[、,，]/).map(t => t.trim()).filter(t => t.length > 0);

                    // 提取内容块（从当前行到下一个空白行）
                    const contentLines: string[] = [lines[i]]; // 包含 RA 标记行
                    let j = i + 1;
                    
                    while (j < lines.length && lines[j].trim() !== '') {
                        contentLines.push(lines[j]);
                        j++;
                    }

                    const raItem: RAContentItem = {
                        id: timestamp,
                        timestamp: timestamp,
                        content: contentLines,
                        topics: topics,
                        sourceFile: filePath,
                        sourceLine: i + 1,
                        fullContent: contentLines.join('\n')
                    };

                    raItems.push(raItem);
                } catch (error) {
                    errors.push(`解析第 ${i + 1} 行的 RA 标记时出错: ${error.message}`);
                }
            }
        }

        return { raItems, errors };
    }

    /**
     * 获取专题笔记信息
     * @param topicName 主题名称
     * @returns 专题笔记信息
     */
    async getTopicNoteInfo(topicName: string): Promise<TopicNoteInfo> {
        const areasFolder = this.app.vault.getAbstractFileByPath(this.areasPath);
        
        if (!areasFolder) {
            throw new Error(`专题目录不存在: ${this.areasPath}`);
        }

        // 获取所有 markdown 文件
        const files = this.app.vault.getMarkdownFiles()
            .filter(file => file.path.startsWith(this.areasPath + '/'));

        // 查找匹配的专题笔记
        for (const file of files) {
            const fileName = file.basename;
            const match = fileName.match(/^(\d+)\s*-\s*(.+)$/);

            if (match) {
                const number = parseInt(match[1]);
                const title = match[2];

                // 简单的主题匹配：直接比较主题名称和笔记标题
                if (title.toLowerCase().trim() === topicName.toLowerCase().trim()) {
                    return {
                        path: file.path,
                        number: number,
                        title: title,
                        exists: true
                    };
                }
            }
        }

        // 如果没有找到匹配的笔记，准备创建新的
        const nextNumber = await this.getNextTopicNumber();
        const newPath = `${this.areasPath}/${nextNumber.toString().padStart(2, '0')} - ${topicName}.md`;
        
        return {
            path: newPath,
            number: nextNumber,
            title: topicName,
            exists: false
        };
    }



    /**
     * 获取下一个专题笔记编号
     * @returns 下一个编号
     */
    private async getNextTopicNumber(): Promise<number> {
        const files = this.app.vault.getMarkdownFiles()
            .filter(file => file.path.startsWith(this.areasPath + '/'));

        let maxNumber = 0;
        
        for (const file of files) {
            const fileName = file.basename;
            const match = fileName.match(/^(\d+)\s*-\s*/);
            
            if (match) {
                const number = parseInt(match[1]);
                if (number > maxNumber) {
                    maxNumber = number;
                }
            }
        }

        return maxNumber + 1;
    }

    /**
     * 同步 RA 内容到专题笔记
     * @param raItems RA 内容项数组
     */
    async syncRAContentToTopicNotes(raItems: RAContentItem[]): Promise<void> {
        // 按主题分组 RA 内容
        const itemsByTopic = new Map<string, RAContentItem[]>();

        for (const item of raItems) {
            for (const topic of item.topics) {
                if (!itemsByTopic.has(topic)) {
                    itemsByTopic.set(topic, []);
                }
                itemsByTopic.get(topic)!.push(item);
            }
        }

        // 处理每个主题
        for (const [topic, items] of itemsByTopic) {
            try {
                await this.syncTopicContent(topic, items);
            } catch (error) {
                console.error(`同步主题 "${topic}" 时出错:`, error);
                new Notice(`同步主题 "${topic}" 失败: ${error.message}`);
            }
        }
    }

    /**
     * 同步单个主题的内容
     * @param topic 主题名称
     * @param items 该主题的 RA 内容项
     */
    private async syncTopicContent(topic: string, items: RAContentItem[]): Promise<void> {
        const topicInfo = await this.getTopicNoteInfo(topic);
        let file: TFile;
        let existingContent = '';

        if (topicInfo.exists) {
            // 读取现有文件
            file = this.app.vault.getAbstractFileByPath(topicInfo.path) as TFile;
            existingContent = await this.app.vault.read(file);
        } else {
            // 创建新文件（不添加一级标题）
            const initialContent = '';
            file = await this.app.vault.create(topicInfo.path, initialContent);
            existingContent = initialContent;
        }

        // 智能更新内容：保护现有非 RA 内容
        const newContent = this.smartUpdateContent(existingContent, items);

        // 更新文件
        await this.app.vault.modify(file, newContent);

        new Notice(`已同步 ${items.length} 个内容块到 "${topic}"`);
    }

    /**
     * 从专题笔记中提取现有的 RA 内容块
     * @param content 文件内容
     * @returns 现有的 RA 内容项
     */
    private extractExistingRAItems(content: string): RAContentItem[] {
        const lines = content.split('\n');
        const raItems: RAContentItem[] = [];
        const raPattern = /^(\d{14})｜Reasoning\s*-\s*Action｜(.+)$/;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            const match = line.match(raPattern);

            if (match) {
                const timestamp = match[1];
                const topicsStr = match[2];
                const topics = topicsStr.split(/[、,，]/).map(t => t.trim()).filter(t => t.length > 0);

                // 提取内容块
                const contentLines: string[] = [lines[i]];
                let j = i + 1;
                
                while (j < lines.length && lines[j].trim() !== '') {
                    contentLines.push(lines[j]);
                    j++;
                }

                raItems.push({
                    id: timestamp,
                    timestamp: timestamp,
                    content: contentLines,
                    topics: topics,
                    sourceFile: '',
                    sourceLine: 0,
                    fullContent: contentLines.join('\n')
                });
            }
        }

        return raItems;
    }

    /**
     * 合并并排序 RA 内容项
     * @param existingItems 现有内容项
     * @param newItems 新内容项
     * @returns 合并后的排序内容项
     */
    private mergeAndSortRAItems(existingItems: RAContentItem[], newItems: RAContentItem[]): RAContentItem[] {
        const itemMap = new Map<string, RAContentItem>();

        // 添加现有项目
        for (const item of existingItems) {
            itemMap.set(item.id, item);
        }

        // 添加新项目（会覆盖同时间戳的现有项目）
        for (const item of newItems) {
            itemMap.set(item.id, item);
        }

        // 转换为数组并按时间戳降序排序
        const allItems = Array.from(itemMap.values());
        allItems.sort((a, b) => b.timestamp.localeCompare(a.timestamp));

        return allItems;
    }

    /**
     * 清理内容中的分隔线
     * @param content 原始内容
     * @returns 清理后的内容
     */
    private removeSeparators(content: string): string {
        const lines = content.split('\n');
        const cleanedLines: string[] = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            // 跳过单独的分隔线（前后都是空行或边界）
            if (line === '---') {
                const prevEmpty = i === 0 || lines[i - 1].trim() === '';
                const nextEmpty = i === lines.length - 1 || lines[i + 1].trim() === '';
                if (prevEmpty && nextEmpty) {
                    continue; // 跳过这个分隔线
                }
            }
            cleanedLines.push(lines[i]);
        }

        return cleanedLines.join('\n');
    }

    /**
     * 智能更新内容：保护现有非 RA 内容，只更新 RA 相关部分
     * @param existingContent 现有内容
     * @param newItems 新的 RA 内容项
     * @param title 笔记标题
     * @returns 更新后的内容
     */
    private smartUpdateContent(existingContent: string, newItems: RAContentItem[]): string {
        // 第一步：清理现有内容中的所有分隔线
        const cleanedContent = this.removeSeparators(existingContent);

        // 第二步：提取现有的 RA 内容
        const existingRAItems = this.extractExistingRAItems(cleanedContent);

        // 第三步：合并新旧内容
        const allItems = this.mergeAndSortRAItems(existingRAItems, newItems);

        // 第四步：重新构建内容，保护非 RA 内容
        return this.rebuildContentWithSeparators(cleanedContent, allItems);
    }

    /**
     * 重新构建内容，保护非 RA 内容并添加分隔线
     * @param cleanedContent 清理后的内容
     * @param allItems 所有 RA 内容项
     * @returns 重新构建的内容
     */
    private rebuildContentWithSeparators(cleanedContent: string, allItems: RAContentItem[]): string {
        const lines = cleanedContent.split('\n');
        const raPattern = /^(\d{14})｜Reasoning\s*-\s*Action｜(.+)$/;
        const newLines: string[] = [];

        // 创建 RA 内容映射
        const raMap = new Map<string, RAContentItem>();
        for (const item of allItems) {
            raMap.set(item.timestamp, item);
        }

        let i = 0;
        while (i < lines.length) {
            const line = lines[i].trim();
            const match = line.match(raPattern);

            if (match) {
                // 这是一个 RA 标记行，跳过原有的 RA 块
                // 跳过整个原有的 RA 块
                while (i < lines.length && lines[i].trim() !== '') {
                    i++;
                }
                // 跳过空行
                while (i < lines.length && lines[i].trim() === '') {
                    i++;
                }
                i--; // 回退一步，因为外层循环会 i++
            } else {
                // 保留非 RA 内容
                newLines.push(lines[i]);
            }
            i++;
        }

        // 添加所有 RA 内容（按时间戳降序）
        if (allItems.length > 0) {
            // 确保在添加 RA 内容前有适当的分隔
            if (newLines.length > 0 && newLines[newLines.length - 1].trim() !== '') {
                newLines.push('');
            }

            for (const item of allItems) {
                newLines.push(item.fullContent);
                newLines.push('');
                newLines.push('---');
                newLines.push('');
            }
        }

        return newLines.join('\n');
    }



    /**
     * 构建专题笔记内容（已废弃，使用 smartUpdateContent 替代）
     * @param title 笔记标题
     * @param items RA 内容项
     * @returns 完整的笔记内容
     */
    private buildTopicNoteContent(title: string, items: RAContentItem[]): string {
        const lines: string[] = [];

        // 添加标题
        lines.push(`# ${title}`);
        lines.push('');

        // 添加 RA 内容块
        for (const item of items) {
            lines.push(item.fullContent);
            lines.push(''); // 添加空行分隔
        }

        return lines.join('\n');
    }
}
