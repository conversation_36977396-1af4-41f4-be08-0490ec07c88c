import { Editor, Notice } from 'obsidian';

// 知识结构生成提示词
export const KNOWLEDGE_STRUCTURE_GENERATE_PROMPT = `你是知识管理和信息架构专家，你的任务是为 Obsidian 知识库中的主题笔记构建一个结构化的知识目录。

## 你的工作流程如下

1.  **理解知识抽象层级：**  你需要理解知识抽象层级的概念 \`Sn[x]\`，它代表知识组织的不同精细化程度。 \`S1\` 是最高层级（领域），后续层级 \`S2, S3, ... Sn\`  代表从领域到具体笔记内容之间逐步细化的概念层级。 **这里的 \`x\` 是一个数字指纹或标识符，用于区分同一层级下的不同概念。** \`Sn\` 最终指向具体的笔记链接，并包含对笔记内容的简明注释。  详细的层级抽象定义请参考下方 "知识抽象层级定义" 部分。
2.  **分析笔记链接和摘要：**  我将提供一系列笔记链接及其摘要，这些链接都来自当前正在编辑的主题笔记。你的任务是分析这些笔记链接和摘要的内容。
3.  **构建知识结构并确定 S1 领域：**  基于你对笔记内容和知识抽象层级的理解，**首先确定当前主题笔记所属的 S1 领域关键词**。然后，将这些笔记链接组织成一个多层结构的知识目录，层级从 S1 领域开始，向下细化到 \`Sn\` 层级。
    *   **S1 领域：**  根据笔记内容和链接，**由你来确定最合适的 S1 领域关键词**。作为知识结构的最顶层节点。
    *   **S2, S3, ... Sx 概念层级：**  从 S1 领域开始，根据笔记内容逐步细化概念层级 (S2, S3, ... Sx)。  这些层级代表了从宏观领域到微观知识点的抽象路径。层级的数量和具体概念由你根据笔记内容合理划分。**在同一层级下，使用 \`concept_id\` (数字序号) 来区分不同的概念。**
    *   **Sn 笔记链接层级：**  最底层的 \`Sn\` 节点对应具体的笔记。**对于 \`Sn\` 层级，\`content_or_note_refrence_id\`  字段需要填写** **输入 JSON 中提供的 \`note_id\` 值**。客户端将使用此 \`note_id\` 来查找对应的笔记标题和摘要，并构建最终的链接和注释。AI无需在响应中包含完整的笔记标题或摘要。


## 知识抽象层级的定义
层级抽象 \`Sn[x]\`：知识组织的精细化描述。层级抽象表示 \`Sn[x]\` 提供了一个灵活的工具，用于刻画知识的不同抽象程度和具体内容。 **这里的 \`x\` 是一个数字指纹或标识符，用于区分同一层级下的不同概念。** 这里的层级 \`n\` 和层级内容 \`x\` 都是相对的概念，其意义取决于知识结构树中的位置。

*   **S1[x]：领域 (Domain)**：  \`n=1\` 时，代表最高的抽象层级，即知识领域。 例如，SwiftUI、物理学、经济学等。  领域层级定义了知识的范围和边界，是知识组织的最顶层框架。 **(由 AI 自动判断和生成)**
*   **S2[x], S3[x], ... Sx[x]：概念层级 (Conceptual Hierarchy)**： \`n=2, 3, ... x\` 时，代表从领域到具体知识点之间的概念细化层级。 例如，在 SwiftUI 领域下，可以有 "声明式语法" (S2),  "状态管理" (S3), "属性包装器" (S4) 等层级。  这些层级将领域知识逐步细分，形成知识组织的主干框架。 **同一层级下的不同概念使用不同的数字标识符 \`x\` (concept_id) 进行区分。**
*   **Sn[x]：笔记链接 (Note Reference) & 注释 (Comment)**：  \`n\` 为知识结构的最深层级时，代表具体的笔记链接。 \`x\`  **对应输入 JSON 中提供的 \`note_id\` 值。** \`Sn\` 层级包含笔记的标题 (隐含在 \`note_id\` 对应的笔记链接中) 和对笔记内容的简明注释 (作为 \`comment\`)。 例如，\`Sn[1]\`，并附带注释 "解释了 @State 的基本用法和原理"。  Sn 层级是知识组织的最末端，指向具体的知识内容来源。

通过 \`Sn[x]\` 的层级划分，我们更灵活地理解知识组织的不同层次和维度。  从 S1 领域开始，沿着 S2, S3, ... Sx 概念层级逐步向下，最终到达 Sn 笔记链接，形成一条从宏观到微观的知识脉络。


## 输入示例
{
  "note_links_with_summaries": [
    {
      "note_id": "1",
      "note_title": "20250124153513｜Swift模块概念与项目文件夹结构详解",
      "summary": "本文详细解释了Swift中模块的概念及其与项目文件夹结构的关系。Swift模块是独立的代码单元，用于代码组织、命名空间管理、代码复用和访问控制。默认情况下，一个Xcode项目的模块是整个项目本身，文件无需显式导入。但当使用Swift Package、Framework或多个Target时，需要显式导入模块。文章还区分了架构设计上的模块化和Swift模块级别的分离，并通过示例进行了详细说明。理解Swift模块对于构建大型、可维护的项目至关重要。"
    },
    {
      "note_id": "2",
      "note_title": "20250125102245｜SwiftUI 声明式UI的优势和核心思想",
      "summary": "本文深入探讨了SwiftUI声明式UI的优势和核心思想。声明式UI通过描述UI的期望状态，而不是编写命令式的步骤来实现UI构建，从而提高了开发效率和代码可维护性。文章对比了声明式UI与命令式UI的区别，并解释了SwiftUI如何利用Swift的DSL（领域特定语言）来实现声明式语法。核心思想包括：状态驱动UI、单向数据流、组合与重用、以及响应式编程。"
    },
    {
      "note_id": "3",
      "note_title": "20250125113852｜SwiftUI 状态管理：@State, @Binding, @ObservedObject 详解",
      "summary": "本文全面解析了SwiftUI中的状态管理机制，重点介绍了 @State, @Binding, @ObservedObject 这三个属性包装器的作用、用法和适用场景。文章解释了 @State 用于管理视图自身的状态，@Binding 用于父子视图之间的数据同步，@ObservedObject 用于监听外部 ObservableObject 的状态变化。通过具体的代码示例，展示了如何在 SwiftUI 中有效地管理和传递数据，实现动态 UI 更新。"
    },
    {
      "note_id": "4",
      "note_title": "20250125145937｜SwiftUI 布局系统：HStack, VStack, ZStack 和 GeometryReader",
      "summary": "本文系统讲解了SwiftUI的布局系统，详细介绍了 HStack, VStack, ZStack 这三种基础布局容器，以及 GeometryReader 的使用方法。文章解释了 HStack, VStack 用于水平和垂直方向的视图排列，ZStack 用于视图的层叠，GeometryReader 用于获取父视图提供的几何信息，从而实现更灵活的布局。通过对比和示例，帮助开发者理解 SwiftUI 布局系统的特点和应用。"
    }
  ]
}

## 输出要求

*   **JSON 格式：**  请以 **JSON** 格式输出完整的知识结构。
*   **严格的 JSON Schema:**  输出必须严格符合以下 JSON Schema \`Node\` 的定义，**不要输出任何 JSON Schema 以外的内容**。
*   **递归结构：**  使用 \`subnode\` 属性表示子节点，构建递归的层级结构。
*   **AI 驱动 S1：**  由 AI 自动判断并生成 S1 领域关键词。
*   **层级灵活性：**  S2, S3, ... Sx 的层级数量和概念由 AI 灵活决定，以最好地组织知识结构。
*   **摘要处理：**  对于 \`Sn\` 层级的节点，客户端将根据 \`content_or_note_refrence_id\` (即 \`note_id\`) 从原始输入中查找并填充笔记标题和摘要。AI的输出中，\`Sn\` 节点的 \`comment\` 字段可以省略或为空字符串。
*   **\`note_id\` 映射：**  **在输出 JSON 中，\`Sn\` 层级的 \`content_or_note_refrence_id\` 字段必须对应输入 JSON 中提供的 \`note_id\` 值。**

**JSON Schema \`Node\` 定义:** 

\`\`\`json
{
  "layer": "string",         // 知识层级，枚举值： "S1", "S2", "S3", "S4", "S5", ... "Sn" (根据实际层级深度可能更多)
  "concept_id": "integer",    //  概念ID，在同一层级下唯一，用于标识概念的序号，从1开始递增
  "content_or_note_refrence_id": "string", //  节点内容 (S1, S2, S3, ... Sx 的概念名称) 或 **笔记链接的 \`note_id\` 值 (Sn)**
  "comment": "string",        //  可选的评论字段。对于 Sn 层级，此字段可由 AI 省略，客户端将根据 note_id 填充摘要。
  "subnode": "array[Node]"     //  子节点数组，可以为空数组
}
\`\`\`

## 输出示例
{
  "layer": "S1",
  "concept_id": 1,
  "content_or_note_refrence_id": "SwiftUI",
  "subnode": [
    {
      "layer": "S2",
      "concept_id": 1,
      "content_or_note_refrence_id": "声明式UI",
      "subnode": [
        {
          "layer": "S3",
          "concept_id": 1,
          "content_or_note_refrence_id": "2",
          "comment": "",
          "subnode": []
        }
      ]
    },
    {
      "layer": "S2",
      "concept_id": 2,
      "content_or_note_refrence_id": "状态管理",
      "subnode": [
        {
          "layer": "S3",
          "concept_id": 1,
          "content_or_note_refrence_id": "3",
          "comment": "",
          "subnode": []
        }
      ]
    },
    {
      "layer": "S2",
      "concept_id": 3,
      "content_or_note_refrence_id": "布局系统",
      "subnode": [
        {
          "layer": "S3",
          "concept_id": 1,
          "content_or_note_refrence_id": "4",
          "comment": "",
          "subnode": []
        }
      ]
    },
    {
      "layer": "S2",
      "concept_id": 4,
      "content_or_note_refrence_id": "模块化",
      "subnode": [
        {
          "layer": "S3",
          "concept_id": 1,
          "content_or_note_refrence_id": "1",
          "comment": "",
          "subnode": []
        }
      ]
    }
  ]
}


解释:

S1 领域 (SwiftUI): 最顶层节点，layer: "S1", content_or_note_refrence_id: "SwiftUI".
S2 概念轴: 在 SwiftUI 领域下，AI 提取了四个核心概念轴： "声明式UI", "状态管理", "布局系统", "模块化"。 它们都是 layer: "S2" 节点，并分别分配了 concept_id: 1, 2, 3, 4。
S3 笔记链接 (Sn): 每个 S2 概念轴下，都有一个 S3 节点，对应一个笔记链接。例如，"声明式UI" 下的 S3 节点：
layer: "S3"
concept_id: 1
content_or_note_refrence_id: "2" 这里对应了输入 JSON 中 note_id: "2" 的笔记。
comment: "" 此处为空，客户端将使用 note_id "2" 从原始输入中查找并填充该笔记的摘要。
subnode: [] S3 节点作为最底层的笔记链接，没有子节点。

请务必是使用中文生成知识结构树，不要使用英文。
`;



// 类型定义
interface KnowledgeNode {
	layer: string;
	concept_id: number;
	content_or_note_refrence_id: string;
	comment?: string;
	subnode: KnowledgeNode[];
}

interface NoteLinkWithSummary {
	note_id: string;
	note_title: string;
	summary: string;
}

interface KnowledgeStructureInput {
	new_note_links_with_summaries: NoteLinkWithSummary[];
	existing_knowledge_structure?: KnowledgeNode;
}

interface KnowledgeTreeGenerationSettings {
	apiEndpoint: string;
	apiKey: string;
}

interface KnowledgeTreeOptions {
	editor: Editor;
	settings: KnowledgeTreeGenerationSettings;
}

// API调用函数
async function callKnowledgeStructureAPI(input: KnowledgeStructureInput, settings: KnowledgeTreeGenerationSettings): Promise<KnowledgeNode | null> {
	try {
		const response = await fetch(`${settings.apiEndpoint}?key=${settings.apiKey}`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				contents: [{
					parts: [
						{ text: KNOWLEDGE_STRUCTURE_GENERATE_PROMPT + "\n\nInput JSON:\n" + JSON.stringify(input, null, 2) }
					]
				}],
				generationConfig: { response_mime_type: "application/json" }
			})
		});

		if (!response.ok) {
			throw new Error(`API 请求失败: ${response.statusText}`);
		}

		const data = await response.json();
		return JSON.parse(data.candidates[0].content.parts[0].text);
	} catch (error) {
		console.error('ERROR - [knowledgeTreeGeneration.ts] 知识结构 API 调用失败:', error);
		return null;
	}
}

// 提取笔记链接和摘要函数
function extractNoteLinksWithSummaries(fullNoteContent: string): NoteLinkWithSummary[] {
	const noteLinks: NoteLinkWithSummary[] = [];
	const lines = fullNoteContent.split('\n');
	let currentId = 1;

	for (const line of lines) {
		// 增强的正则表达式，支持两种格式：
		// 1. [[FILENAME_OR_ID|DISPLAY_TEXT]]｜SUMMARY_TEXT
		// 2. [[FILENAME_OR_ID]]｜SUMMARY_TEXT
		const linkSummaryRegex = /\[\[([^\]|]+)(?:\|([^\]]+))?\]\]｜(.+)/;
		const match = line.match(linkSummaryRegex);
		
		if (match) {
			const [_, filenameOrId, displayText, summary] = match;
			
			// 确定note_title：优先使用displayText，如果不存在则使用filenameOrId
			const noteTitle = displayText ? displayText.trim() : filenameOrId.trim();
			
			noteLinks.push({
				note_id: currentId.toString(),
				note_title: noteTitle,
				summary: summary.trim()
			});
			currentId++;
		}
	}

	// 去重：基于note_title和summary的组合进行去重
	const uniqueNoteLinks: NoteLinkWithSummary[] = [];
	const seen = new Set<string>();
	
	for (const noteLink of noteLinks) {
		const key = `${noteLink.note_title}|${noteLink.summary}`;
		if (!seen.has(key)) {
			seen.add(key);
			uniqueNoteLinks.push(noteLink);
		}
	}

	return uniqueNoteLinks;
}

// 从现有知识结构树中提取链接的函数
function extractLinksFromExistingTree(knowledgeTreeContent: string): string[] {
	const extractedLinks: string[] = [];
	const lines = knowledgeTreeContent.split('\n');
	
	console.info("INFO - [knowledgeTreeGeneration.ts][extractLinksFromExistingTree_001] 开始解析旧知识结构树");
	
	for (const line of lines) {
		// 匹配知识结构树中的链接格式：
		// - [[笔记标题]]
		// - [[笔记标题]]｜摘要内容
		// - [[文件名|显示文本]]｜摘要内容
		const linkPattern = /^\s*-\s*\[\[([^|\]]+)(?:\|([^|\]]+))?\]\](?:｜(.*))?/;
		const match = line.match(linkPattern);
		
		if (match) {
			const [_, filenameOrId, displayText, summary] = match;
			
			// 构建提取的链接格式：[[笔记标题]]｜摘要
			let noteTitle = displayText ? displayText.trim() : filenameOrId.trim();
			let linkText = `[[${noteTitle}]]`;
			
			if (summary && summary.trim()) {
				linkText += `｜${summary.trim()}`;
			}
			
			extractedLinks.push(linkText);
			console.info(`INFO - [knowledgeTreeGeneration.ts][extractLinksFromExistingTree_002] 提取链接: ${linkText}`);
		}
	}
	
	console.info(`INFO - [knowledgeTreeGeneration.ts][extractLinksFromExistingTree_003] 共提取 ${extractedLinks.length} 个链接`);
	return extractedLinks;
}

// 格式化知识结构函数
function formatKnowledgeStructure(
	node: KnowledgeNode, 
	level: number = 0, 
	noteSummaries: NoteLinkWithSummary[] = []
): string {
	let result = '';
	const isSnNode = node.subnode.length === 0 && /^\d+$/.test(node.content_or_note_refrence_id);

	if (isSnNode) {
		// Sn 节点 (笔记链接和摘要) - 作为正文内容
		const noteId = node.content_or_note_refrence_id;
		const noteInfo = noteSummaries.find(ns => ns.note_id === noteId);
		if (noteInfo) {
			result += `[[${noteInfo.note_title}]]\n`;
			if (noteInfo.summary && noteInfo.summary.trim() !== "") {
				result += `${noteInfo.summary}\n`;
			}
		} else {
			result += `[[Note ID: ${noteId}]]\n`;
			result += `Summary not found for this ID.\n`;
		}
		result += '\n'; // Add an extra newline after each Sn block for separation
	} else {
		// 概念节点 (S1, S2, etc.) - 使用 Markdown 标题
		const headingLevel = level + 2; // S1 (level 0) -> H2, S2 (level 1) -> H3, etc.
		const headingMarker = '#'.repeat(headingLevel);
		let contentToDisplay = node.content_or_note_refrence_id.replace(/^\[\[|\]\]$/g, '');
		
		result += `${headingMarker} ${contentToDisplay}\n\n`; // Two newlines after concept heading

		// 如果概念节点有评论，也作为正文显示 (虽然不常见)
		if (node.comment && node.comment.trim() !== "") {
			result += `${node.comment}\n\n`;
		}
	}

	// 递归处理子节点 (只为概念节点递归，Sn节点是叶子)
	if (!isSnNode && node.subnode && node.subnode.length > 0) {
		node.subnode.forEach(child => {
			result += formatKnowledgeStructure(child, level + 1, noteSummaries);
		});
	}
	return result;
}



// 处理知识结构函数
async function processKnowledgeStructure(
	aiResponseJsonString: string, // Renamed for clarity
	noteSummaries: NoteLinkWithSummary[] // This is the crucial part
): Promise<string> {
	try {
		const cleanJson = cleanJsonResponse(aiResponseJsonString);
		console.info("INFO - [knowledgeTreeGeneration.ts] Cleaned AI response JSON:", cleanJson);
		const newStructure: KnowledgeNode = JSON.parse(cleanJson);
		console.info("INFO - [knowledgeTreeGeneration.ts] Parsed new knowledge structure:", JSON.stringify(newStructure, null, 2));

		// Pass noteSummaries to formatKnowledgeStructure
		return formatKnowledgeStructure(newStructure, 0, noteSummaries);
	} catch (error) {
		console.error('ERROR - [knowledgeTreeGeneration.ts] Error processing knowledge structure:', error);
		throw error; // Re-throw to be caught by handleKnowledgeStructure
	}
}





// 从AI生成的树中提取所有笔记ID
function extractNoteIdsFromTree(node: KnowledgeNode | null): string[] {
	if (!node) {
		return [];
	}

	const ids: string[] = [];
	
	// 检查当前节点是否是Sn节点（表示笔记引用的叶子节点）
	// 假设Sn节点是叶子节点且其content_or_note_refrence_id是数字ID
	const isSnNode = node.subnode.length === 0 && /^\d+$/.test(node.content_or_note_refrence_id);

	if (isSnNode) {
		ids.push(node.content_or_note_refrence_id);
	}

	// 递归处理子节点
	if (node.subnode && node.subnode.length > 0) {
		for (const child of node.subnode) {
			ids.push(...extractNoteIdsFromTree(child));
		}
	}
	
	return ids;
}

// 根据ID获取笔记标题函数
function getNoteTitleById(noteId: string, noteSummaries: NoteLinkWithSummary[]): string {
	const note = noteSummaries.find(n => n.note_id === noteId);
	return note ? note.note_title : noteId;
}

// 根据ID列表获取笔记标题列表
function getTitlesForIds(ids: string[], allNoteLinks: NoteLinkWithSummary[], includeId: boolean = false): string[] {
	return ids.map(id => {
		const noteInfo = allNoteLinks.find(link => link.note_id === id);
		if (noteInfo) {
			return includeId ? `${noteInfo.note_title} (ID: ${id})` : noteInfo.note_title;
		}
		return `未知ID: ${id}`;
	}).filter(title => title !== null) as string[];
}

// 清理JSON响应函数
function cleanJsonResponse(jsonString: string): string {
	// 移除可能的 markdown 代码块标记
	return jsonString.replace(/^\`\`\`json\n|\`\`\`$/g, '').trim();
}

// 主要功能函数
export async function handleKnowledgeStructure(options: KnowledgeTreeOptions): Promise<void> {
	const { editor, settings } = options;

	try {
		// 获取用户选择的文本内容，如果没有选择则使用完整内容
		const selection = editor.getSelection();
		const selectedText = selection.trim();
		
		if (!selectedText) {
			new Notice('请先选择包含笔记链接和摘要的文本内容');
			return;
		}

		console.info("INFO - [knowledgeTreeGeneration.ts][handleKnowledgeStructure_001] 处理选择的文本内容，长度:", selectedText.length);

		// 从选择的内容中提取笔记链接和摘要
		const noteLinks = extractNoteLinksWithSummaries(selectedText);
		if (noteLinks.length === 0) {
			new Notice('选择的文本中未找到有效的笔记链接和摘要');
			return;
		}
		console.info("INFO - [knowledgeTreeGeneration.ts][handleKnowledgeStructure_002] 最终提取的笔记摘要:", JSON.stringify(noteLinks, null, 2));

		// 准备 API 输入
		const input: KnowledgeStructureInput = {
			new_note_links_with_summaries: noteLinks
		};
		console.info("INFO - [knowledgeTreeGeneration.ts][handleKnowledgeStructure_003] 准备的 API 输入:", JSON.stringify(input, null, 2));

		// 调用 AI API
		new Notice('正在生成知识结构...');
		const result = await callKnowledgeStructureAPI(input, settings);
		if (!result) {
			new Notice('生成知识结构失败');
			return;
		}
		console.info("INFO - [knowledgeTreeGeneration.ts][handleKnowledgeStructure_004] AI API 返回结果:", JSON.stringify(result, null, 2));

		// 处理并格式化知识结构
		const formattedStructure = await processKnowledgeStructure(
			JSON.stringify(result), // The raw JSON string from AI
			noteLinks // The array of {note_id, note_title, summary}
		);

		// 输入输出比对与提示
		console.info("INFO - [knowledgeTreeGeneration.ts][handleKnowledgeStructure_005] 开始输入输出比对分析");

		// 1. 获取输入ID集合
		const inputNoteIdSet = new Set(noteLinks.map(nl => nl.note_id));

		// 2. 获取AI输出ID列表
		const rawOutputNoteIds: string[] = extractNoteIdsFromTree(result);
		const uniqueOutputNoteIdSet = new Set(rawOutputNoteIds);

		// 3. 进行比对分析
		// 遗漏项：输入有但输出没有
		const omittedNoteIds = [...inputNoteIdSet].filter(id => !uniqueOutputNoteIdSet.has(id));

		// AI新增项：输出有但输入没有
		const unexpectedlyAddedIds = [...uniqueOutputNoteIdSet].filter(id => !inputNoteIdSet.has(id));

		// AI结构中的重复引用
		const hasInternalDuplicatesInOutput = rawOutputNoteIds.length > uniqueOutputNoteIdSet.size;
		let duplicatedInOutputTitles: string[] = [];
		if (hasInternalDuplicatesInOutput) {
			const idCounts: Record<string, number> = {};
			rawOutputNoteIds.forEach(id => {
				idCounts[id] = (idCounts[id] || 0) + 1;
			});
			const duplicatedIds = Object.entries(idCounts)
									.filter(([id, count]) => count > 1)
									.map(([id, count]) => id);
			duplicatedInOutputTitles = getTitlesForIds(duplicatedIds, noteLinks, true);
		}

		// 4. 构建并显示通知
		let noticeParts: string[] = [];
		if (omittedNoteIds.length === 0 && unexpectedlyAddedIds.length === 0 && inputNoteIdSet.size === uniqueOutputNoteIdSet.size) {
			noticeParts.push(`输入 ${inputNoteIdSet.size} / 输出 ${uniqueOutputNoteIdSet.size}，知识结构构建准确。`);
		} else {
			noticeParts.push(`输入 ${inputNoteIdSet.size} / AI输出唯一笔记 ${uniqueOutputNoteIdSet.size} (总引用 ${rawOutputNoteIds.length}).`);
			if (omittedNoteIds.length > 0) {
				const titles = getTitlesForIds(omittedNoteIds, noteLinks);
				noticeParts.push(`遗漏项 (${omittedNoteIds.length}): ${titles.join('; ')}`);
			}
			if (unexpectedlyAddedIds.length > 0) {
				noticeParts.push(`AI额外添加的未知项 (${unexpectedlyAddedIds.length}): ${unexpectedlyAddedIds.join('; ')}`);
			}
		}

		if (hasInternalDuplicatesInOutput && duplicatedInOutputTitles.length > 0) {
			if (omittedNoteIds.length === 0 && unexpectedlyAddedIds.length === 0 && inputNoteIdSet.size === uniqueOutputNoteIdSet.size) {
				noticeParts.push(`注意: 部分笔记在结构中被多次引用: ${duplicatedInOutputTitles.join('; ')}`);
			} else {
				noticeParts.push(`AI结构内存在重复引用。重复项: ${duplicatedInOutputTitles.join('; ')}`);
			}
		}

		console.info("INFO - [knowledgeTreeGeneration.ts][handleKnowledgeStructure_006] 比对分析完成", {
			inputCount: inputNoteIdSet.size,
			uniqueOutputCount: uniqueOutputNoteIdSet.size,
			totalOutputReferences: rawOutputNoteIds.length,
			omittedCount: omittedNoteIds.length,
			unexpectedCount: unexpectedlyAddedIds.length,
			hasDuplicates: hasInternalDuplicatesInOutput
		});

		// 构建比对结果信息
		let comparisonInfo = '';
		if (noticeParts.length > 0) {
			comparisonInfo = '\n<!-- 输入输出比对结果 -->\n';
			comparisonInfo += '> [!info] 知识结构构建分析\n';
			for (const part of noticeParts) {
				comparisonInfo += `> ${part}\n`;
			}
		}

		// 构建知识结构内容（去除一级标题）
		const structureContent = `${formattedStructure}${comparisonInfo}`;

		// 将生成的知识结构替换到用户选择的位置
		editor.replaceSelection(structureContent);

		// 显示简化的成功通知
		if (noticeParts.length === 1 && noticeParts[0].includes('知识结构构建准确')) {
			new Notice('✨ 知识结构生成完成！构建准确。', 3000);
		} else {
			// 如果有问题，显示简要提醒，详细信息在知识结构树中查看
			const issueCount = omittedNoteIds.length + unexpectedlyAddedIds.length + (hasInternalDuplicatesInOutput ? 1 : 0);
			new Notice(`✨ 知识结构生成完成！发现 ${issueCount} 个问题，详细信息请查看知识结构下方的分析。`, 5000);
		}
	} catch (error) {
		console.error('ERROR - [knowledgeTreeGeneration.ts][handleKnowledgeStructure_007] 处理知识结构时出错:', error);
		new Notice('处理知识结构失败: ' + error.message);
	}
}
