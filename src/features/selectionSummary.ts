import { Editor, Notice } from 'obsidian';

// 类型定义
interface SelectionSummarySettings {
	apiEndpoint: string;
	apiKey: string;
}

interface SelectionSummaryOptions {
	editor: Editor;
	settings: SelectionSummarySettings;
}

// 提示词
export const GENERATE_TITLE_PROMPT = `Detail Summary Title: Write a detailed summary of the notes, helping users decide whether to engage with the content.The summary should be concise but informative (50-200 words), should capture the key points and main ideas of the notes, Ensure summaries are detailed enough to serve as good search targets

请以JSON格式输出结果，只包含一个键"summary"，值为生成的摘要，要求输出语言为中文。例如：{"summary": "生成的中文摘要内容"}

待分析文本：
{TEXT_CONTENT}`;

// API调用函数
async function callGenerateSummaryAPI(
	selectedText: string,
	settings: SelectionSummarySettings
): Promise<string | null> {
	try {
		if (!settings.apiEndpoint || !settings.apiKey) {
			throw new Error('API配置不完整，请检查设置');
		}
		if (!selectedText) {
			throw new Error('选中的文本不能为空');
		}

		const promptText = GENERATE_TITLE_PROMPT.replace('{TEXT_CONTENT}', selectedText);

		const requestBody = {
			contents: [{
				parts: [{ text: promptText }]
			}],
			generationConfig: {
				temperature: 1,
				maxOutputTokens: 8192,
				response_mime_type: "application/json"
			}
		};

		console.log('摘要生成 API 请求体:', JSON.stringify(requestBody, null, 2));

		const response = await fetch(`${settings.apiEndpoint}?key=${settings.apiKey}`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify(requestBody)
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error('API 请求失败:', response.status, response.statusText, errorText);
			throw new Error(`API 请求失败: ${response.statusText}`);
		}

		const data = await response.json();
		console.log('摘要生成 API 原始响应:', JSON.stringify(data, null, 2));

		if (!data.candidates?.[0]?.content?.parts?.[0]?.text) {
			console.error('API 响应格式不正确:', data);
			throw new Error('API 响应格式不正确');
		}

		const responseText = data.candidates[0].content.parts[0].text;
		console.log('API 响应文本:', responseText);

		try {
			const parsedResponse = JSON.parse(responseText);
			if (parsedResponse && typeof parsedResponse.summary === 'string' && parsedResponse.summary.trim()) {
				console.log('成功提取摘要:', parsedResponse.summary);
				return parsedResponse.summary.trim();
			} else {
				console.error('解析后的 JSON 响应缺少 summary 字段或为空:', parsedResponse);
				throw new Error('AI 返回的摘要无效');
			}
		} catch (parseError) {
			console.error('解析 JSON 响应失败:', parseError, '原始文本:', responseText);
			// 尝试直接将返回的文本作为摘要（如果AI没有完全按JSON格式返回）
			if (typeof responseText === 'string' && responseText.trim().length > 0 && responseText.length < 1000) {
				console.warn('解析JSON失败，尝试直接使用返回文本作为摘要');
				return responseText.trim();
			}
			throw new Error('解析 AI 响应失败: ' + parseError.message);
		}
	} catch (error) {
		console.error('摘要生成 API 调用失败:', error);
		new Notice(`摘要生成 API 调用失败: ${error.message}`);
		return null;
	}
}

// 主要功能函数
export async function generateSummaryForSelection(options: SelectionSummaryOptions): Promise<void> {
	const { editor, settings } = options;
	
	const originalSelection = editor.getSelection(); // 保留原始选择，包括可能的首尾空格
	const selectionForAPI = originalSelection.trim(); // 去掉首尾空格用于API

	if (!selectionForAPI) {
		new Notice('请先选择需要生成摘要的文本');
		return;
	}

	try {
		new Notice('🚀 正在生成摘要...');
		const generatedSummary = await callGenerateSummaryAPI(selectionForAPI, settings);

		if (generatedSummary) {
			// 移除标题中可能存在的Markdown标题标记（如 #）
			const cleanSummary = generatedSummary.replace(/^#+\s*/, '').trim();
			// 格式化最终替换内容
			const newContent = `摘要： ${cleanSummary}\n\n${originalSelection}`; // 使用原始选择保留格式
			editor.replaceSelection(newContent);
			new Notice('✅ 摘要生成并替换成功！');
		} else {	
			new Notice('❌ 无法生成摘要');
		}
	} catch (error) {
		console.error('为选中内容生成摘要时出错:', error);
		new Notice('生成摘要时出错: ' + error.message);
	}
}
