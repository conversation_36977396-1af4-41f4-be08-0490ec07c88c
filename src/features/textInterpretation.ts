import { Editor, Notice } from 'obsidian';

// 类型定义
interface TextInterpretationSettings {
	apiEndpoint: string;
	apiKey: string;
}

interface TextInterpretationOptions {
	editor: Editor;
	settings: TextInterpretationSettings;
	selectedText: string;
}

// 提示词
export const CODE_INTERPRETER_PROMPT = `作为一名耐心且善于使用费曼技巧的老师，请你详细解读选中的技术文档，并进行内容转换（润色、排版和重写）和构建知识体系（还原论和线性时间观）
- 在解答的时候，着重使用两种技巧：还原论和线性时间观点，提供关于概念静态与动态的知识。
	1. 还原论，也就是定位到知识系统底层的一些不可再分概念，你需要讲解这些概念的定义，以及不同概念之间它们发生互动的关系。
	2. 线性时间观的运作图景，就是这个系统它是怎么通过连续的操作，步骤和指令达到最终预期的状态。这个状态是什么？这个结果是什么？它是怎么来实现这个需求的？
# Steps

1. 理解内容: 
	- 仔细阅读用户提供的内容，识别核心概念、关键信息和潜在的难点。
	- 根据用户提供的内容，明确本次学习的背景，重点和目标，即用户最需要掌握的知识和技能。
2. 角色扮演: 扮演一位耐心且善于运用费曼技巧的老师，
3. 内容转换:
    - 将理解后的文档内容用中文表达出来。
	- 对内容进行格式化:
		- 使用 markdown 格式对内容进行标记，包括加粗、斜体、列表和少量 emoji 表情。
		- 保留和修复标题和表格的格式。以保证原文的结构不被破坏。
		- 修复公式中的错误
		- 代码块处理:
			- 识别文档中的代码块。
			- 确认代码块的编程语言。
			- 美化代码，包括重新组织缩进，并使用合适的语法高亮。
			- 保留代码块中的中间代码。
4. 系统化整理用户提供的知识
	- 知识系统化： 将用户提供的知识进行梳理，建立逻辑结构，区分主次，并补充必要的背景知识。
	- 问题-答案设计： 将系统化的知识点转化为一系列问题，每个问题对应一个或多个知识点。
	- 详细解答： 使用简洁明了的语言，结合上下文背景，详细解答每个问题。
	- 运用费曼技巧： 在解释过程中，尝试用简单的比喻、类比等方法，将复杂的概念转化为易于理解和记忆的形式。
	- 总结和应用： 在解答完所有问题后，对本次学习内容进行总结，从如何理解逻辑，如何解决问题，如何实现目标，如何运用知识等角度，并引导用户思考如何在实际应用中运用这些知识。
5. 输出: 
	- 不要输出任何引导语，不要提及你扮演的角色，直接输出指导内容
	- 以 markdown 格式返回最终的润色、排版和重写后的文档内容。

# Output Format
# Examples
## input
To simplify the usage of Extensions, Google provides some out of the box extensions that can be quickly imported into your project and used with minimal configurations. For example, the Code Interpreter extension in Snippet 1 allows you to generate and run Python code from a natural language description.
\`\`\`
Python import vertexai import pprint PROJECT_ID = "YOUR_PROJECT_ID" REGION = "us-central1" vertexai.init(project=PROJECT_ID, location=REGION) from vertexai.preview.extensions import Extension extension_code_interpreter = Extension.from_hub("code_interpreter") CODE_QUERY = """Write a python method to invert a binary tree in O(n) time.""" response = extension_code_interpreter.execute( operation_id = "generate_and_execute", operation_params = {"query": CODE_QUERY} ) print("Generated Code:") pprint.pprint({response['generated_code']}) # The above snippet will generate the following code. ''' Generated Code: class TreeNode: def __init__(self, val=0, left=None, right=None): self.val = val self.left = left self.right = right Continues next page...
\`\`\`
## output
为了简化扩展的使用，Google 提供了一些开箱即用的扩展，可以快速导入到您的项目中并以最少的配置使用。例如，代码片段 1 中的代码解释器扩展允许您从自然语言描述生成和运行 Python 代码。

文档提到了Google提供的一些现成的扩展。 其中一个例子就是 代码解释器(Code Interpreter)，它可以让你用自然语言来生成和执行Python代码。 就像你跟一个会写代码的朋友👨🏼‍💻说，帮我写一段代码，然后他就能帮你搞定。

\`\`\`python
import vertexai
import pprint

PROJECT_ID = "YOUR_PROJECT_ID"
REGION = "us-central1"

vertexai.init(project=PROJECT_ID, location=REGION)

from vertexai.preview.extensions import Extension

extension_code_interpreter = Extension.from_hub("code_interpreter")

CODE_QUERY = """Write a python method to invert a binary tree in O(n) time."""

response = extension_code_interpreter.execute(
    operation_id = "generate_and_execute",
    operation_params = {"query": CODE_QUERY}
)

print("Generated Code:")
pprint.pprint({response['generated_code']})

# The above snippet will generate the following code.
'''
Generated Code:
class TreeNode:
    def __init__(self, val=0, left=None, right=None):
        self.val = val
        self.left = left
        self.right = right
Continues next page...
'''
\`\`\`
#### 问题1：代码解释器扩展的作用是什么？
代码解释器扩展就像一个"智能程序员"，你只需要告诉它你想做什么，比如"反转一个二叉树"，它就能理解你的意图，并自动生成相应的 Python 代码。更重要的是，它不仅能生成代码，还能执行这些代码，并返回运行结果。

这使得你可以在不离开 Vertex AI 环境的情况下，快速测试想法、生成代码片段和执行数据处理任务。这种方式非常高效，尤其是在需要快速原型设计或进行实验时。

我们可以从线性时间观的角度来看待这个过程：

1. 你提供指令：你用自然语言（例如："写一个 Python 方法反转二叉树"）告诉代码解释器你的需求。
2. 代码生成：代码解释器根据你的指令，分析你的意图，并生成相应的 Python 代码。
3. 代码执行：代码解释器执行生成的 Python 代码。
4. 返回结果：代码解释器返回执行结果，例如反转二叉树的代码。
整个过程就像一个流水线，你只需要给出输入，代码解释器就会自动完成后续的任务，最终给你输出结果。
# Notes

   确保输出内容使用中文，并且流畅易懂。
   费曼技巧的运用需要自然且贴合内容，不要为了使用技巧而强行使用。
   在代码块处理时，要注意代码的正确性和美观性，确保缩进和语法高亮正确。
   输出的 markdown 格式要规范，方便 Obsidian 插件处理。
   避免使用标题标签，防止破坏原文档的结构。
   emoji 的使用要少量适量，返回所有文字不超过2个，避免影响阅读体验。`;

// API调用函数
async function callInterpreterAPI(
	content: string,
	settings: TextInterpretationSettings
): Promise<string | null> {
	try {
		const response = await fetch(`${settings.apiEndpoint}?key=${settings.apiKey}`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				contents: [{
					parts: [
						{ text: CODE_INTERPRETER_PROMPT + "\n\nText to analyze:\n" + content }
					]
				}],
				generationConfig: {
					temperature: 1,
					topK: 40,
					topP: 0.95,
					maxOutputTokens: 8192,
				}
			})
		});

		if (!response.ok) {
			throw new Error(`API 请求失败: ${response.statusText}`);
		}

		const data = await response.json();
		return data.candidates[0].content.parts[0].text;
	} catch (error) {
		console.error('解释器 API 调用失败:', error);
		return null;
	}
}

// 主要功能函数
export async function interpretSelectedText(options: TextInterpretationOptions): Promise<void> {
	const { editor, settings, selectedText } = options;
	
	try {
		new Notice('正在解释文本...');
		const response = await callInterpreterAPI(selectedText, settings);
		
		if (!response) {
			new Notice('无法获取 AI 响应');
			return;
		}

		// 格式化响应内容
		const formattedResponse = `\`讲解\`\n\n${response}\n\n---\n`;

		// 替换选中的文本
		editor.replaceSelection(formattedResponse);
		new Notice('✨ 解释完成！');
	} catch (error) {
		console.error('解释文本时出错:', error);
		new Notice('解释文本时出错: ' + error.message);
	}
}
