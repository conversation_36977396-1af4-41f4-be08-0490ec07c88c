import { Editor, Notice } from 'obsidian';

// 类型定义
interface KeywordExtractionSettings {
	apiEndpoint: string;
	apiKey: string;
}

interface KeywordExtractionOptions {
	editor: Editor;
	settings: KeywordExtractionSettings;
}

// 提示词
export const KEYWORD_EXTRACTION_PROMPT = `请仔细阅读以下文本，提取其中最核心的 3-5 个主题或关键词。

# 任务要求：
1.  识别文本内容的主要议题、概念或对象。
2.  提取能够代表这些核心内容的关键词或短语。
3.  关键词应简洁明了。
4.  **严格按照以下格式输出：** 将所有提取的关键词用中文的全角竖线 \`｜\` 连接成一个单一的字符串。例如："关键词1｜关键词2｜关键词3"。
5.  **不要包含任何其他文字、解释、列表标记或换行符，仅返回最终的关键词字符串。**
6.  请使用中文进行关键词提取和输出。

# 待分析文本：
{TEXT_CONTENT}
`;

// API调用函数
async function callKeywordExtractionAPI(
	selectedText: string, 
	settings: KeywordExtractionSettings
): Promise<string | null> {
	try {
		if (!settings.apiEndpoint || !settings.apiKey) {
			throw new Error('API配置不完整，请检查设置');
		}

		if (!selectedText) {
			throw new Error('选中的文本为空');
		}

		const promptText = KEYWORD_EXTRACTION_PROMPT.replace('{TEXT_CONTENT}', selectedText);
		
		const requestBody = {
			contents: [
				{
					parts: [
						{ text: promptText }
					]
				}
			],
			generationConfig: {
				temperature: 0.2,
				topK: 32,
				topP: 0.95,
				maxOutputTokens: 1024,
			},
		};

		console.log('关键词提取 API 请求体:', JSON.stringify(requestBody, null, 2));

		const response = await fetch(`${settings.apiEndpoint}?key=${settings.apiKey}`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify(requestBody)
		});

		if (!response.ok) {
			throw new Error(`API 请求失败: ${response.statusText}`);
		}

		const data = await response.json();
		console.log('关键词提取 API 原始响应:', JSON.stringify(data, null, 2));

		if (!data.candidates?.[0]?.content?.parts?.[0]?.text) {
			throw new Error('API 响应格式不正确或无有效内容');
		}

		const extractedKeywords = data.candidates[0].content.parts[0].text.trim();
		console.log('提取的关键词字符串:', extractedKeywords);

		// 如果 AI 返回空字符串，也视为提取失败
		if (!extractedKeywords) {
			new Notice('AI 未能提取有效的关键词');
			return null;
		}

		return extractedKeywords;

	} catch (error) {
		console.error('关键词提取 API 调用失败:', error);
		new Notice(`关键词提取 API 调用失败: ${error.message}`);
		return null;
	}
}

// 主要功能函数
export async function handleKeywordExtraction(options: KeywordExtractionOptions): Promise<void> {
	const { editor, settings } = options;
	
	// 获取原始选区，不使用 trim() 以保留原文格式
	const originalSelection = editor.getSelection(); 

	if (!originalSelection) {
		new Notice('请先选择需要提取主题键的文本');
		return;
	}

	// 去掉首尾空白用于传递给 API
	const textForAPI = originalSelection.trim();
	if (!textForAPI) {
		new Notice('选中的文本内容为空');
		return;
	}

	try {
		new Notice('正在提取主题键...');
		const keywordString = await callKeywordExtractionAPI(textForAPI, settings);

		if (keywordString !== null) {
			// 构建新的替换内容：主题键 + 换行 + 原始选中文本
			const newContent = `\n\`\`\`\n索引｜${keywordString}\n${originalSelection}\n\`\`\``; 

			// 替换选中的文本
			editor.replaceSelection(newContent);
			new Notice('✨ 主题键提取并与原文组合完成！');
		} else {
			console.log('未能获取有效的关键词字符串，不执行替换');
		}
	} catch (error) {
		console.error('处理主题键提取时出错:', error);
		new Notice('处理主题键提取失败: ' + error.message);
	}
}
