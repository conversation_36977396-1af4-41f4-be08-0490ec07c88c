import { TFile, Notice, App, MarkdownView } from 'obsidian';

// 类型定义
interface NoteNode {
	path: string;
	children: NoteNode[];
}

interface KnowledgeDocumentExportOptions {
	app: App;
}

// 查找笔记函数 (修改以在整个仓库查找)
async function findNoteInRoot(notePath: string, app: App): Promise<TFile | null> {
	try {
		// 定义聚焦文件夹路径 (不再需要用于限制搜索范围，但可以保留作为信息)
		// const focusPath = '03-仓库/01-笔记/01-聚焦';

		// 获取 Obsidian vault 中的所有 Markdown 文件 (现在搜索范围是整个仓库)
		const files = app.vault.getMarkdownFiles();

		// 从 notePath 中提取时间戳或文件名
		const noteId = notePath.replace('.md', '');

		console.info(`查找笔记: ${noteId}`);
		// console.info(`在路径下查找: ${focusPath}`); // 这行日志不再准确，可以删除或修改

		// 查找匹配的文件
		const matchedFile = files.find(file => {
			// 不再检查文件是否在聚焦文件夹中
			// const isInFocusFolder = file.path.startsWith(focusPath);

			// 检查文件名是否匹配
			const isMatch = file.basename === noteId || file.basename.startsWith(noteId + '｜');

			// 如果需要，可以在这里添加日志来查看正在检查的文件路径
			// console.info(`正在检查文件: ${file.path}, 文件名匹配: ${isMatch}`);

			// 现在只需要文件名匹配即可
			if (isMatch) {
				console.info(`找到匹配的笔记: ${file.path}`);
				return true;
			}
			return false;
		});

		if (!matchedFile) {
			console.warn(`未找到笔记 ${noteId} 在整个仓库中，已检查文件数量: ${files.length}`);
			// 如果需要，可以在这里列出检查过的文件名，但可能输出会非常长
			// console.warn('已检查的文件:', files.map(f => f.path));
		}

		return matchedFile || null;
	} catch (error) {
		console.error('搜索笔记时出错:', error);
		throw error;
	}
}

// 处理单个笔记函数
async function processNote(node: NoteNode, visitedNotes: Set<string>, app: App): Promise<void> {
	try {
		// 如果已经访问过，则跳过
		if (visitedNotes.has(node.path)) {
			return;
		}
		
		// 标记为已访问
		visitedNotes.add(node.path);
		
		// 读取笔记内容
		const file = app.vault.getAbstractFileByPath(node.path);
		if (!(file instanceof TFile)) {
			return;
		}
		
		const content = await app.vault.read(file);
		
		// 匹配所有的内部链接 [[...]]
		const linkRegex = /\[\[(.*?)\]\]/g;
		const matches = content.matchAll(linkRegex);
		
		for (const match of matches) {
			const noteName = match[1];
			// 构建完整路径（添加.md扩展名）
			const notePath = `${noteName}.md`;
			
			// 在 Obsidian 根目录下搜索文件
			const linkedFile = await findNoteInRoot(notePath, app);
			if (linkedFile) {
				const childNode: NoteNode = {
					path: linkedFile.path,
					children: []
				};
				node.children.push(childNode);
				
				// 递归处理链接的笔记
				await processNote(childNode, visitedNotes, app);
			}
		}
	} catch (error) {
		console.error('处理单个笔记时出错:', error);
		throw error;
	}
}

// 生成合并内容函数
async function generateMergedContent(rootNode: NoteNode, app: App): Promise<string> {
	try {
		let mergedContent = '';
		
		// 递归生成合并内容
		const processNode = async (node: NoteNode) => {
			const file = app.vault.getAbstractFileByPath(node.path);
			if (!(file instanceof TFile)) {
				return;
			}
			
			// 读取笔记内容
			const content = await app.vault.read(file);
			// 使用80个连字符作为分隔符
			const separator = '-'.repeat(80);
			mergedContent += `\n\n# ${file.basename}\n\n${content}\n\n${separator}\n`;
			
			// 处理子节点
			for (const child of node.children) {
				await processNode(child);
			}
		};
		
		await processNode(rootNode);
		return mergedContent.trim();
	} catch (error) {
		console.error('生成合并内容时出错:', error);
		throw error;
	}
}

// 主要功能函数
export async function processNoteLinks(options: KnowledgeDocumentExportOptions): Promise<void> {
	const { app } = options;
	
	try {
		const view = app.workspace.getActiveViewOfType(MarkdownView);
		if (!view || !view.file) {
			new Notice('未找到活动的 Markdown 视图');
			return;
		}

		// 获取当前笔记内容
		const content = await app.vault.read(view.file);
		
		// 创建访问记录集合
		const visitedNotes = new Set<string>();
		
		// 创建根节点
		const rootNode: NoteNode = {
			path: view.file.path,
			children: []
		};
		
		// 处理笔记链接
		await processNote(rootNode, visitedNotes, app);
		
		// 生成合并后的内容
		const mergedContent = await generateMergedContent(rootNode, app);
		
		// 使用更精确的时间戳格式 (年月日时分秒)
		const now = new Date();
		const timestamp = [
			now.getFullYear(),
			String(now.getMonth() + 1).padStart(2, '0'),
			String(now.getDate()).padStart(2, '0'),
			String(now.getHours()).padStart(2, '0'),
			String(now.getMinutes()).padStart(2, '0'),
			String(now.getSeconds()).padStart(2, '0')
		].join('');
		
		// 获取当前笔记名称
		const currentNoteName = view.file.basename;
		
		// 导出到下载目录，使用 "时间戳｜笔记名称导出.md" 格式
		const exportPath = `/Users/<USER>/Downloads/${timestamp}｜${currentNoteName}导出.md`;
		
		// 使用 Node.js 的 fs 模块写入文件
		const fs = require('fs');
		fs.writeFileSync(exportPath, mergedContent);
		
		new Notice(`知识文档已导出到: ${exportPath}`);
	} catch (error) {
		console.error('处理笔记链接时出错:', error);
		new Notice('处理笔记链接失败: ' + error.message);
	}
}
