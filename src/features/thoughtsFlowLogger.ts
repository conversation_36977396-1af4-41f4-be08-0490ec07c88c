import { Editor, Notice, TFile, App } from 'obsidian';

// 类型定义
interface ThoughtsFlowSettings {
	thoughtsPath: string;
}

interface ThoughtsFlowOptions {
	editor: Editor;
	app: App;
	settings: ThoughtsFlowSettings;
	selectedContent: string;
}

// 主要功能函数
export async function addToThoughtsFlow(options: ThoughtsFlowOptions): Promise<void> {
	const { editor, app, settings, selectedContent } = options;
	
	try {
		// 获取当前时间并格式化
		const now = new Date();
		const year = now.getFullYear();
		const month = now.getMonth() + 1;
		const day = now.getDate();
		const hour = String(now.getHours()).padStart(2, '0');
		const minute = String(now.getMinutes()).padStart(2, '0');
		
		// 构建格式化的时间戳
		const timestamp = `${year}年${month}月${day}日 ${hour}:${minute}`;

		// 构建要添加的内容，使用反引号包裹时间戳
		const contentToAdd = `\`${timestamp}\`\n${selectedContent}\n\n`;

		// 获取想法瀑布流文件
		const thoughtsPath = settings.thoughtsPath.startsWith('/') 
			? settings.thoughtsPath.slice(1) 
			: settings.thoughtsPath;

		// 确保目标文件夹存在
		const folderPath = thoughtsPath.substring(0, thoughtsPath.lastIndexOf('/'));
		if (!await app.vault.adapter.exists(folderPath)) {
			await app.vault.createFolder(folderPath);
		}

		// 获取或创建想法瀑布流文件
		let thoughtsFile = app.vault.getAbstractFileByPath(thoughtsPath);
		if (!thoughtsFile) {
			thoughtsFile = await app.vault.create(thoughtsPath, contentToAdd);
		} else if (thoughtsFile instanceof TFile) {
			// 读取现有内容
			const existingContent = await app.vault.read(thoughtsFile);
			// 在顶部添加新内容
			await app.vault.modify(thoughtsFile, contentToAdd + existingContent);
		}

		// 在原编辑区域，保留内容，在content上一行添加 #已记录
		editor.replaceSelection(`#已记录\n${selectedContent}`);

		new Notice('✅ 已添加到想法瀑布流！');
	} catch (error) {
		console.error('添加到想法瀑布流时出错:', error);
		new Notice('添加到想法瀑布流失败: ' + error.message);
	}
}
