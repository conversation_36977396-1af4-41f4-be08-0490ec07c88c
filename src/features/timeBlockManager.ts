import { Editor, Notice } from 'obsidian';

// 类型定义
interface TimeBlockOptions {
	editor: Editor;
}

// 主要功能函数
export async function insertTimeBlock(options: TimeBlockOptions): Promise<void> {
	const { editor } = options;
	
	try {
		// 获取选中的内容
		const selectedContent = editor.getSelection().trim();

		// 如果没有选中内容，插入当前时间的时间块
		if (!selectedContent) {
			const now = new Date();
			const hour = String(now.getHours()).padStart(2, '0');
			const minute = String(now.getMinutes()).padStart(2, '0');
			
			// 构建时间块模板
			const timeBlock = `\`${hour}:${minute} -> ${hour}:${minute}｜0 分钟\``;
			
			// 在当前光标位置插入时间块
			editor.replaceSelection(timeBlock);
			
			new Notice('✨ 已插入时间块！');
			return;
		}

		// 如果有选中内容，尝试解析和计算时间差
		const timePattern = /(\d{2}:\d{2})\s*->\s*(\d{2}:\d{2})/;
		const match = selectedContent.match(timePattern);

		if (!match) {
			new Notice('❌ 所选内容不符合时间块格式！');
			return;
		}

		const [_, startTime, endTime] = match;
		
		// 解析开始和结束时间
		const [startHour, startMinute] = startTime.split(':').map(Number);
		const [endHour, endMinute] = endTime.split(':').map(Number);

		// 创建日期对象进行计算
		const start = new Date();
		start.setHours(startHour, startMinute, 0);
		
		const end = new Date();
		end.setHours(endHour, endMinute, 0);

		// 如果结束时间小于开始时间，假设跨越了一天
		if (end < start) {
			end.setDate(end.getDate() + 1);
		}

		// 计算时间差（毫秒）
		const diffMs = end.getTime() - start.getTime();
		const diffMinutes = Math.floor(diffMs / (1000 * 60));
		
		// 计算小时和分钟
		const hours = Math.floor(diffMinutes / 60);
		const minutes = diffMinutes % 60;

		// 构建时间描述
		let timeDescription = '';
		if (hours > 0) {
			timeDescription += `${hours}小时`;
		}
		if (minutes > 0 || hours === 0) {
			timeDescription += `${minutes}分钟`;
		}

		// 构建新的时间块
		const newTimeBlock = `\`${startTime} -> ${endTime}｜${timeDescription}\``;
		
		// 替换选中内容
		editor.replaceSelection(newTimeBlock);
		
		new Notice('✨ 已更新时间块！');
	} catch (error) {
		console.error('处理时间块时出错:', error);
		new Notice('处理时间块失败: ' + error.message);
	}
}
