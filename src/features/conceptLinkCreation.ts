import { Editor, Notice, TFile, App } from 'obsidian';

// 类型定义
interface ConceptLinkSettings {
	conceptPath: string;
	obsidianRoot: string;
}

interface ConceptLinkOptions {
	editor: Editor;
	app: App;
	settings: ConceptLinkSettings;
	selectedText: string;
}

interface RelatedNote {
	file: TFile;
	title: string;
	summary: string;
}



// 提取文件摘要
function extractSummary(content: string): string {
	const lines = content.split('\n');

	// 查找以"摘要："开头的行
	for (let i = 0; i < Math.min(10, lines.length); i++) {
		const line = lines[i].trim();
		if (line.startsWith('摘要：')) {
			return line.substring(3).trim();
		}
	}

	// 如果没找到摘要，返回前100个字符
	const cleanContent = content
		.replace(/^#.*$/gm, '') // 移除标题
		.replace(/!\[.*?\]\(.*?\)/g, '') // 移除图片
		.replace(/\[.*?\]\(.*?\)/g, '') // 移除链接
		.trim();

	return cleanContent.substring(0, 100) + (cleanContent.length > 100 ? '...' : '');
}

// 搜索相关笔记
async function searchRelatedNotes(app: App, concept: string, searchPath: string): Promise<RelatedNote[]> {
	const relatedNotes: RelatedNote[] = [];
	const allFiles = app.vault.getMarkdownFiles();

	// 过滤出搜索路径下的文件
	const normalizedSearchPath = searchPath.replace(/^\//, '');
	const searchFiles = allFiles.filter(file =>
		file.path.startsWith(normalizedSearchPath)
	);

	// 支持多种搜索模式
	const searchTerms = [
		concept.toLowerCase(),
		concept.toLowerCase().replace(/\s+/g, ''), // 去空格版本
		concept.toLowerCase().replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '') // 只保留字母数字中文
	].filter((term, index, arr) => arr.indexOf(term) === index); // 去重

	for (const file of searchFiles) {
		try {
			const content = await app.vault.read(file);
			const normalizedContent = content.toLowerCase();

			// 检查文件内容是否包含任一搜索词
			const hasMatch = searchTerms.some(term =>
				normalizedContent.includes(term)
			);

			if (hasMatch) {
				const summary = extractSummary(content);
				relatedNotes.push({
					file,
					title: file.basename,
					summary
				});
			}
		} catch (error) {
			console.error(`读取文件 ${file.path} 时出错:`, error);
		}
	}

	return relatedNotes;
}

// 生成概念文件内容
function generateConceptContent(concept: string, relatedNotes: RelatedNote[]): string {
	let content = `# ${concept}\n\n## 定义\n\n> 请在此处添加 ${concept} 的定义和解释。\n\n## 相关概念\n\n## 相关笔记\n\n`;

	if (relatedNotes.length > 0) {
		relatedNotes.forEach(note => {
			content += `[[${note.title}]]\n摘要：${note.summary}\n\n`;
		});
	} else {
		content += `暂无相关笔记。\n\n`;
	}

	content += `## 参考资料\n\n`;

	return content;
}

// 主要功能函数
export async function createConceptLink(options: ConceptLinkOptions): Promise<void> {
	const { editor, app, settings, selectedText } = options;

	try {
		if (!selectedText || selectedText.trim().length === 0) {
			new Notice('请先选择要创建概念链接的文本');
			return;
		}

		const originalConcept = selectedText.trim();

		// 获取概念存储路径
		const conceptPath = settings.conceptPath.startsWith('/')
			? settings.conceptPath.slice(1)
			: settings.conceptPath;

		// 确保目标文件夹存在
		if (!await app.vault.adapter.exists(conceptPath)) {
			await app.vault.createFolder(conceptPath);
		}

		// 检查是否已存在相同概念的文件
		const conceptFileName = `${originalConcept}.md`;
		const conceptFilePath = `${conceptPath}/${conceptFileName}`;
		const fileExists = await app.vault.adapter.exists(conceptFilePath);

		// 搜索相关笔记 - 使用相对路径
		const searchPath = '03-仓库';
		new Notice('🔍 正在搜索相关笔记...');

		const relatedNotes = await searchRelatedNotes(app, originalConcept, searchPath);

		// 生成概念文件内容
		const conceptContent = generateConceptContent(originalConcept, relatedNotes);

		if (fileExists) {
			// 如果文件已存在，覆盖更新
			const existingFile = app.vault.getAbstractFileByPath(conceptFilePath) as TFile;
			if (existingFile) {
				await app.vault.modify(existingFile, conceptContent);
				new Notice(`🔄 概念 "${originalConcept}" 已更新！找到 ${relatedNotes.length} 个相关笔记`);
			}
		} else {
			// 创建新的概念文件
			await app.vault.create(conceptFilePath, conceptContent);
			new Notice(`✨ 概念 "${originalConcept}" 已创建！找到 ${relatedNotes.length} 个相关笔记`);
		}

		// 在原编辑区域插入链接
		const linkText = `[[${originalConcept}]]`;
		editor.replaceSelection(linkText);
	} catch (error) {
		console.error('创建概念链接时出错:', error);
		new Notice('创建概念链接失败: ' + error.message);
	}
}

