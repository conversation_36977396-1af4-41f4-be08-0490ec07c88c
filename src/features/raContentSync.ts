import { App, Editor, MarkdownView, Notice, TFile } from 'obsidian';
import { RAContentSyncManager } from './raContentSyncManager';

/**
 * 处理 RA 内容同步的主要函数
 */
export async function handleRAContentSync(params: {
    app: App;
    editor?: Editor;
    file?: TFile;
}): Promise<void> {
    const { app, editor, file } = params;

    try {
        // 获取当前活动的文件和编辑器
        let currentFile = file;
        let currentEditor = editor;

        if (!currentFile || !currentEditor) {
            const activeView = app.workspace.getActiveViewOfType(MarkdownView);
            if (!activeView) {
                new Notice('请先打开一个 Markdown 文件');
                return;
            }
            currentFile = activeView.file || undefined;
            currentEditor = activeView.editor;
        }

        if (!currentFile) {
            new Notice('无法获取当前文件');
            return;
        }

        // 获取文件内容
        const content = currentEditor.getValue();
        
        // 创建 RA 内容同步管理器
        const syncManager = new RAContentSyncManager(app);
        
        // 扫描 RA 内容
        new Notice('正在扫描 RA 内容...');
        const scanResult = syncManager.scanRAContent(content, currentFile.path);
        
        if (scanResult.errors.length > 0) {
            console.warn('扫描过程中发现错误:', scanResult.errors);
            for (const error of scanResult.errors) {
                new Notice(`扫描错误: ${error}`, 5000);
            }
        }

        if (scanResult.raItems.length === 0) {
            new Notice('未找到符合格式的 RA 内容标记');
            return;
        }

        new Notice(`找到 ${scanResult.raItems.length} 个 RA 内容块，正在同步...`);
        
        // 同步到专题笔记
        await syncManager.syncRAContentToTopicNotes(scanResult.raItems);
        
        // 统计同步的主题数量
        const topicSet = new Set<string>();
        for (const item of scanResult.raItems) {
            for (const topic of item.topics) {
                topicSet.add(topic);
            }
        }

        new Notice(`✅ 成功同步 ${scanResult.raItems.length} 个内容块到 ${topicSet.size} 个专题笔记`);
        
    } catch (error) {
        console.error('RA 内容同步失败:', error);
        new Notice(`❌ RA 内容同步失败: ${error.message}`);
    }
}

/**
 * 预览 RA 内容（用于调试和确认）
 */
export async function previewRAContent(params: {
    app: App;
    editor?: Editor;
    file?: TFile;
}): Promise<void> {
    const { app, editor, file } = params;

    try {
        // 获取当前活动的文件和编辑器
        let currentFile = file;
        let currentEditor = editor;

        if (!currentFile || !currentEditor) {
            const activeView = app.workspace.getActiveViewOfType(MarkdownView);
            if (!activeView) {
                new Notice('请先打开一个 Markdown 文件');
                return;
            }
            currentFile = activeView.file || undefined;
            currentEditor = activeView.editor;
        }

        if (!currentFile) {
            new Notice('无法获取当前文件');
            return;
        }

        // 获取文件内容
        const content = currentEditor.getValue();
        
        // 创建 RA 内容同步管理器
        const syncManager = new RAContentSyncManager(app);
        
        // 扫描 RA 内容
        const scanResult = syncManager.scanRAContent(content, currentFile.path);
        
        if (scanResult.raItems.length === 0) {
            new Notice('未找到符合格式的 RA 内容标记');
            return;
        }

        // 构建预览信息
        const previewLines: string[] = [];
        previewLines.push(`# RA 内容预览`);
        previewLines.push(`\n找到 ${scanResult.raItems.length} 个 RA 内容块：\n`);

        for (const item of scanResult.raItems) {
            previewLines.push(`## 时间戳: ${item.timestamp}`);
            previewLines.push(`**主题**: ${item.topics.join('、')}`);
            previewLines.push(`**源文件**: ${item.sourceFile} (第 ${item.sourceLine} 行)`);
            previewLines.push(`**内容**:`);
            previewLines.push('```');
            previewLines.push(item.fullContent);
            previewLines.push('```');
            previewLines.push('');
        }

        // 统计主题信息
        const topicStats = new Map<string, number>();
        for (const item of scanResult.raItems) {
            for (const topic of item.topics) {
                topicStats.set(topic, (topicStats.get(topic) || 0) + 1);
            }
        }

        previewLines.push(`## 主题统计`);
        for (const [topic, count] of topicStats) {
            previewLines.push(`- **${topic}**: ${count} 个内容块`);
        }

        // 创建预览文件
        const previewContent = previewLines.join('\n');
        const previewFileName = `RA内容预览-${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}.md`;
        
        await app.vault.create(previewFileName, previewContent);
        
        // 打开预览文件
        const previewFile = app.vault.getAbstractFileByPath(previewFileName) as TFile;
        if (previewFile) {
            await app.workspace.getLeaf().openFile(previewFile);
        }

        new Notice(`✅ 已生成 RA 内容预览文件: ${previewFileName}`);
        
    } catch (error) {
        console.error('生成 RA 内容预览失败:', error);
        new Notice(`❌ 生成预览失败: ${error.message}`);
    }
}
