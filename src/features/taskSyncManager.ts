import { App, TFile } from 'obsidian';
import { TaskItem, TaskType } from '../types';

/**
 * 任务状态同步管理器 - 负责在源文件和任务面板之间同步任务状态
 */
export class TaskSyncManager {
    private app: App;

    constructor(app: App) {
        this.app = app;
    }

    /**
     * 将任务面板中的状态同步到源文件
     * @param tasks 原始任务列表
     * @param panelStates 任务面板中的状态映射
     */
    async syncStatesFromPanelToSource(tasks: TaskItem[], panelStates: Map<string, boolean>): Promise<void> {
        // 按源文件分组任务
        const tasksByFile = this.groupTasksByFile(tasks);

        for (const [filePath, fileTasks] of tasksByFile) {
            try {
                await this.updateTaskStatesInFile(filePath, fileTasks, panelStates);
            } catch (error) {
                console.error(`同步文件 ${filePath} 的任务状态时出错:`, error);
            }
        }
    }

    /**
     * 将源文件中的状态同步到任务面板
     * @param tasks 任务列表（会被修改以反映最新状态）
     */
    async syncStatesFromSourceToTasks(tasks: TaskItem[]): Promise<void> {
        const tasksByFile = this.groupTasksByFile(tasks);

        for (const [filePath, fileTasks] of tasksByFile) {
            try {
                await this.readTaskStatesFromFile(filePath, fileTasks);
            } catch (error) {
                console.error(`读取文件 ${filePath} 的任务状态时出错:`, error);
            }
        }
    }

    /**
     * 按源文件分组任务
     * @param tasks 任务列表
     * @returns 按文件分组的任务
     */
    private groupTasksByFile(tasks: TaskItem[]): Map<string, TaskItem[]> {
        const grouped = new Map<string, TaskItem[]>();

        for (const task of tasks) {
            if (!grouped.has(task.sourceFile)) {
                grouped.set(task.sourceFile, []);
            }
            grouped.get(task.sourceFile)!.push(task);
        }

        return grouped;
    }

    /**
     * 更新文件中的任务状态
     * @param filePath 文件路径
     * @param fileTasks 该文件中的任务列表
     * @param panelStates 任务面板中的状态映射
     */
    private async updateTaskStatesInFile(
        filePath: string, 
        fileTasks: TaskItem[], 
        panelStates: Map<string, boolean>
    ): Promise<void> {
        const file = this.app.vault.getAbstractFileByPath(filePath);
        if (!file || !(file instanceof TFile)) {
            throw new Error(`文件不存在: ${filePath}`);
        }

        const content = await this.app.vault.read(file);
        const lines = content.split('\n');
        let modified = false;

        // 按行号排序任务，从后往前处理以避免行号偏移
        const sortedTasks = fileTasks.sort((a, b) => b.sourceLine - a.sourceLine);

        for (const task of sortedTasks) {
            const panelState = panelStates.get(task.id);
            if (panelState !== undefined && panelState !== task.completed) {
                // 状态发生变化，需要更新
                const lineIndex = task.sourceLine - 1;
                if (lineIndex >= 0 && lineIndex < lines.length) {
                    const updatedLine = this.updateTaskStateInLine(lines[lineIndex], panelState);
                    if (updatedLine !== lines[lineIndex]) {
                        lines[lineIndex] = updatedLine;
                        modified = true;
                    }
                }
            }
        }

        if (modified) {
            await this.app.vault.modify(file, lines.join('\n'));
        }
    }

    /**
     * 从文件中读取任务状态并更新任务对象
     * @param filePath 文件路径
     * @param fileTasks 该文件中的任务列表
     */
    private async readTaskStatesFromFile(filePath: string, fileTasks: TaskItem[]): Promise<void> {
        const file = this.app.vault.getAbstractFileByPath(filePath);
        if (!file || !(file instanceof TFile)) {
            return;
        }

        const content = await this.app.vault.read(file);
        const lines = content.split('\n');

        for (const task of fileTasks) {
            const lineIndex = task.sourceLine - 1;
            if (lineIndex >= 0 && lineIndex < lines.length) {
                const currentState = this.extractTaskStateFromLine(lines[lineIndex]);
                if (currentState !== null) {
                    task.completed = currentState;
                }
            }
        }
    }

    /**
     * 更新行中的任务状态
     * @param line 原始行
     * @param completed 新的完成状态
     * @returns 更新后的行
     */
    private updateTaskStateInLine(line: string, completed: boolean): string {
        const newState = completed ? 'x' : ' ';
        return line.replace(/\[([ x])\]/, `[${newState}]`);
    }

    /**
     * 从行中提取任务状态
     * @param line 文本行
     * @returns 完成状态或 null（如果不是任务行）
     */
    private extractTaskStateFromLine(line: string): boolean | null {
        const match = line.match(/\[([ x])\]/);
        if (!match) {
            return null;
        }
        return match[1].toLowerCase() === 'x';
    }
}
