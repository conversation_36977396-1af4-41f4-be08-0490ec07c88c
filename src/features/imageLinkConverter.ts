import { Editor, Notice } from 'obsidian';

// 类型定义
interface ImageLinkOptions {
	editor: Editor;
}

// 主要功能函数
export async function handleImageLinks(options: ImageLinkOptions): Promise<void> {
	const { editor } = options;
	
	const content = editor.getValue();
	// 使用正则表达式匹配markdown格式的图片链接
	const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g;
	
	let newContent = content.replace(imageRegex, (match, altText, imagePath) => {
		// 获取图片文件名
		const fileName = imagePath.split('/').pop();
		if (!fileName) return match;
		
		// 如果已经是相对路径（没有包含协议或绝对路径），则跳过
		if (!imagePath.includes('http') && !imagePath.startsWith('/')) {
			return match;
		}
		
		// 构建新的相对路径链接
		return `![${altText}](./${fileName})`;
	});
	
	// 如果内容有变化，更新编辑器
	if (newContent !== content) {
		editor.setValue(newContent);
		new Notice('图片链接已更新为相对路径');
	} else {
		new Notice('未找到需要处理的图片链接');
	}
}
