import { Editor, Notice } from 'obsidian';

/**
 * 任务层级信息接口（修复版 - 支持绝对缩进保持）
 */
export interface TaskHierarchy {
    level: number;              // 相对缩进级别（0为顶级，用于建立父子关系）
    parentIndex?: number;       // 父任务索引（在原始任务列表中的位置）
    children: number[];         // 子任务索引数组（在原始任务列表中的位置）
    indentSize: number;         // 绝对缩进空格数（用于输出时保持原始位置）
    absoluteIndent: string;     // 原始缩进字符串（包含Tab和空格的原始格式）
}

/**
 * 日程项接口（扩展支持层级关系）
 */
export interface ScheduleItem {
    startTime: string;      // 开始时间 HH:MM
    endTime?: string;       // 结束时间 HH:MM（可选）
    duration: number;       // 持续时间（分钟）
    taskName: string;       // 任务名称
    originalLine: string;   // 原始行内容
    lineIndex: number;      // 行索引
    prefix: string;         // 行前缀（如 "1. [ ] "）
    hierarchy?: TaskHierarchy; // 层级信息（可选，用于支持子任务）
}

/**
 * 普通任务项接口（扩展支持层级关系）
 */
export interface PlainTaskItem {
    taskName: string;       // 任务名称
    originalLine: string;   // 原始行内容
    lineIndex: number;      // 行索引
    prefix: string;         // 行前缀（如 "1. [ ] "）
    hierarchy?: TaskHierarchy; // 层级信息（可选，用于支持子任务）
}

/**
 * 任务组接口（主任务 + 子任务的组合）
 */
export interface TaskGroup {
    mainTask: ScheduleItem | PlainTaskItem;    // 主任务（可以是日程任务或普通任务）
    subTasks: (ScheduleItem | PlainTaskItem)[]; // 子任务数组
    type: 'schedule' | 'plain';                 // 任务组类型
    originalOrder: number;                      // 在原始文本中的顺序
}

/**
 * 解析结果接口（扩展支持层级结构）
 */
export interface ParseResult {
    scheduleItems: ScheduleItem[];      // 日程任务列表
    plainTasks: PlainTaskItem[];        // 普通任务列表
    taskGroups: TaskGroup[];            // 任务组列表（包含层级关系）
    hasHierarchy: boolean;              // 是否包含层级结构
}

/**
 * 日程管理器选项接口
 */
interface ScheduleManagerOptions {
    editor: Editor;
    enableHierarchy?: boolean;  // 是否启用层级处理（默认true）
}

/**
 * 日程管理器 - 处理日程时间安排和冲突解决（支持层级结构）
 */
export class ScheduleManager {
    private editor: Editor;
    private enableHierarchy: boolean;

    constructor(options: ScheduleManagerOptions) {
        this.editor = options.editor;
        this.enableHierarchy = options.enableHierarchy ?? true;
    }

    /**
     * 检测行的缩进级别（修复版 - 保持绝对缩进位置）
     * @param line 文本行
     * @returns 缩进信息 { level: 相对层级, indentSize: 绝对空格数, isValid: 是否有效, absoluteIndent: 原始缩进字符串 }
     */
    private detectIndentLevel(line: string): {
        level: number;
        indentSize: number;
        isValid: boolean;
        absoluteIndent: string;
    } {
        // 匹配前导空白字符
        const match = line.match(/^(\s*)/);
        const indentStr = match ? match[1] : '';
        const indentSize = indentStr.length;

        // 检查缩进是否一致（只包含空格或只包含Tab）
        const hasSpaces = indentStr.includes(' ');
        const hasTabs = indentStr.includes('\t');
        const isValid = !(hasSpaces && hasTabs); // 不允许混合使用空格和Tab

        // 计算相对层级（用于建立父子关系）
        let level = 0;
        if (hasTabs) {
            // Tab模式：每个Tab = 4个空格 = 1个层级
            const normalizedSize = indentStr.replace(/\t/g, '    ').length;
            level = Math.floor(normalizedSize / 4);
        } else {
            // 空格模式：每4个空格 = 1个层级（更严格的层级检测）
            level = Math.floor(indentSize / 4);
        }

        return {
            level,
            indentSize,
            isValid,
            absoluteIndent: indentStr  // 保存原始缩进字符串
        };
    }

    /**
     * 检查行是否为空行或只包含空白字符
     * @param line 文本行
     * @returns 是否为空行
     */
    private isEmptyLine(line: string): boolean {
        return line.trim().length === 0;
    }

    /**
     * 标准化缩进（将tab转换为空格）
     * @param line 文本行
     * @returns 标准化后的行
     */
    private normalizeIndent(line: string): string {
        // 将tab转换为4个空格
        return line.replace(/\t/g, '    ');
    }

    /**
     * 建立任务层级关系（增强版）
     * @param allTasks 所有任务的数组（包括日程任务和普通任务）
     * @returns 包含层级信息的任务数组
     */
    private buildTaskHierarchy(allTasks: (ScheduleItem | PlainTaskItem)[]): (ScheduleItem | PlainTaskItem)[] {
        if (!this.enableHierarchy || allTasks.length === 0) {
            console.log('ScheduleManager: 层级处理已禁用或任务列表为空');
            return allTasks;
        }

        console.log(`ScheduleManager: 开始建立 ${allTasks.length} 个任务的层级关系`);

        const tasksWithHierarchy = [...allTasks];
        const parentStack: { index: number; level: number; task: ScheduleItem | PlainTaskItem }[] = []; // 父任务栈
        let hierarchyCount = 0;

        for (let i = 0; i < tasksWithHierarchy.length; i++) {
            const task = tasksWithHierarchy[i];
            const { level, indentSize, isValid, absoluteIndent } = this.detectIndentLevel(task.originalLine);

            console.log(`ScheduleManager: 处理任务 ${i}, 层级 ${level}, 绝对缩进 ${indentSize}, 有效 ${isValid}, 缩进字符: "${absoluteIndent}"`);

            // 如果缩进格式无效，作为顶级任务处理
            if (!isValid) {
                console.warn(`ScheduleManager: 检测到无效的缩进格式，作为顶级任务处理，行 ${i}: ${task.originalLine}`);
                // 清空父任务栈，将此任务作为新的顶级任务
                parentStack.length = 0;
                const hierarchy: TaskHierarchy = {
                    level: 0,
                    indentSize: 0,
                    absoluteIndent: '',
                    children: []
                };
                task.hierarchy = hierarchy;
                parentStack.push({ index: i, level: 0, task });
                continue;
            }

            // 初始化层级信息（保持绝对缩进信息）
            const hierarchy: TaskHierarchy = {
                level,
                indentSize,
                absoluteIndent,
                children: []
            };

            // 如果是顶级任务（level = 0）
            if (level === 0) {
                parentStack.length = 0; // 清空父任务栈
                parentStack.push({ index: i, level, task });
                console.log(`ScheduleManager: 设置顶级任务 ${i}`);
            } else {
                // 寻找合适的父任务
                // 从栈顶开始，找到第一个层级小于当前任务的任务作为父任务
                while (parentStack.length > 0 && parentStack[parentStack.length - 1].level >= level) {
                    parentStack.pop();
                }

                if (parentStack.length > 0) {
                    const parentInfo = parentStack[parentStack.length - 1];
                    hierarchy.parentIndex = parentInfo.index;

                    // 将当前任务添加到父任务的子任务列表中
                    const parentTask = tasksWithHierarchy[parentInfo.index];
                    if (parentTask.hierarchy) {
                        parentTask.hierarchy.children.push(i);
                        hierarchyCount++;
                        console.log(`ScheduleManager: 任务 ${i} 成为任务 ${parentInfo.index} 的子任务`);
                    }
                } else {
                    // 没有找到合适的父任务，作为顶级任务处理
                    console.warn(`ScheduleManager: 任务 ${i} 没有找到合适的父任务，作为顶级任务处理`);
                    hierarchy.level = 0;
                    hierarchy.indentSize = 0;
                }

                // 将当前任务加入栈中，作为后续任务的潜在父任务
                parentStack.push({ index: i, level, task });
            }

            // 将层级信息附加到任务上
            task.hierarchy = hierarchy;
        }

        console.log(`ScheduleManager: 层级关系建立完成，共建立 ${hierarchyCount} 个父子关系`);
        return tasksWithHierarchy;
    }

    /**
     * 将任务组织成任务组结构
     * @param allTasks 包含层级信息的所有任务
     * @returns 任务组数组
     */
    private organizeTaskGroups(allTasks: (ScheduleItem | PlainTaskItem)[]): TaskGroup[] {
        const taskGroups: TaskGroup[] = [];
        const processedIndices = new Set<number>();

        for (let i = 0; i < allTasks.length; i++) {
            if (processedIndices.has(i)) {
                continue; // 已经作为子任务处理过
            }

            const task = allTasks[i];

            // 如果是顶级任务（没有父任务）
            if (!task.hierarchy || task.hierarchy.level === 0) {
                const subTasks: (ScheduleItem | PlainTaskItem)[] = [];

                // 收集所有子任务
                this.collectSubTasks(allTasks, i, subTasks, processedIndices);

                // 确定任务组类型
                const isScheduleTask = 'startTime' in task;
                const taskGroup: TaskGroup = {
                    mainTask: task,
                    subTasks,
                    type: isScheduleTask ? 'schedule' : 'plain',
                    originalOrder: i
                };

                taskGroups.push(taskGroup);
                processedIndices.add(i);
            }
        }

        return taskGroups;
    }

    /**
     * 递归收集子任务
     * @param allTasks 所有任务数组
     * @param parentIndex 父任务索引
     * @param subTasks 子任务收集数组
     * @param processedIndices 已处理的索引集合
     */
    private collectSubTasks(
        allTasks: (ScheduleItem | PlainTaskItem)[],
        parentIndex: number,
        subTasks: (ScheduleItem | PlainTaskItem)[],
        processedIndices: Set<number>
    ): void {
        const parentTask = allTasks[parentIndex];
        if (!parentTask.hierarchy) {
            return;
        }

        for (const childIndex of parentTask.hierarchy.children) {
            if (childIndex < allTasks.length) {
                const childTask = allTasks[childIndex];
                subTasks.push(childTask);
                processedIndices.add(childIndex);

                // 递归收集子任务的子任务
                this.collectSubTasks(allTasks, childIndex, subTasks, processedIndices);
            }
        }
    }

    /**
     * 处理选中的日程内容（支持层级结构，增强边界情况处理）
     */
    async processSelectedSchedule(): Promise<void> {
        try {
            const selectedText = this.editor.getSelection();

            // 边界情况1：空输入检查
            if (!selectedText || !selectedText.trim()) {
                new Notice('请先选择包含日程的文本内容');
                return;
            }

            // 边界情况2：输入长度检查
            if (selectedText.length > 10000) {
                new Notice('选中的文本过长，请选择较小的文本块进行处理');
                return;
            }

            console.log('ScheduleManager: 开始解析选中文本，启用层级处理:', this.enableHierarchy);
            console.log('ScheduleManager: 输入文本长度:', selectedText.length);

            // 解析选中的日程内容（支持层级结构）
            const parseResult = this.parseScheduleText(selectedText);
            const { scheduleItems, plainTasks, taskGroups, hasHierarchy } = parseResult;

            // 边界情况3：无有效任务检查
            if (scheduleItems.length === 0 && plainTasks.length === 0) {
                new Notice('未找到有效的任务格式。支持格式：\n• 日程任务：时间｜持续时间｜任务名称\n• 普通任务：[ ] 任务名称');
                return;
            }

            // 边界情况4：任务数量检查
            const totalTasks = scheduleItems.length + plainTasks.length;
            if (totalTasks > 100) {
                new Notice(`任务数量过多（${totalTasks}个），建议分批处理以确保性能`);
                // 继续处理，但给出警告
            }

            console.log('ScheduleManager: 解析结果 -', {
                scheduleItems: scheduleItems.length,
                plainTasks: plainTasks.length,
                taskGroups: taskGroups.length,
                hasHierarchy
            });

            // 性能监控：开始计时
            const startTime = Date.now();

            // 处理日程项和任务组
            let processedItems: ScheduleItem[] = [];
            let changes: { timeAdjustments: number; durationCorrections: number; endTimeAdditions: number };

            try {
                if (this.enableHierarchy && hasHierarchy && taskGroups.length > 0) {
                // 使用任务组处理逻辑
                const { processedTaskGroups, changes: groupChanges } = this.processTaskGroups(taskGroups);
                changes = groupChanges;

                // 从处理后的任务组中提取日程项用于文本生成
                processedItems = this.extractScheduleItemsFromGroups(processedTaskGroups);

                // 生成层级结构文本
                const newText = this.generateHierarchicalTaskText(processedTaskGroups, processedItems);
                this.editor.replaceSelection(newText);
                console.log('ScheduleManager: 使用任务组处理和层级结构生成文本');
            } else {
                // 使用传统的单项处理逻辑
                const result = this.processScheduleItems(scheduleItems);
                processedItems = result.processedItems;
                changes = result.changes;

                // 生成传统混合文本
                const newText = this.generateMixedTaskText(plainTasks, processedItems);
                this.editor.replaceSelection(newText);
                console.log('ScheduleManager: 使用传统方式处理和生成文本');
            }

            // 性能监控：结束计时
            const endTime = Date.now();
            const processingTime = endTime - startTime;
            console.log(`ScheduleManager: 处理完成，耗时 ${processingTime}ms`);

            // 性能警告
            if (processingTime > 2000) {
                console.warn(`ScheduleManager: 处理时间较长 (${processingTime}ms)，建议优化或减少任务数量`);
            }

            // 显示处理结果
            this.showMixedProcessingResults(plainTasks.length, processedItems.length, changes);

            } catch (processingError) {
                console.error('ScheduleManager: 处理过程中发生错误:', processingError);

                // 错误恢复：尝试使用传统模式处理
                try {
                    console.log('ScheduleManager: 尝试使用传统模式进行错误恢复');
                    const fallbackResult = this.processScheduleItems(scheduleItems);
                    const fallbackText = this.generateMixedTaskText(plainTasks, fallbackResult.processedItems);
                    this.editor.replaceSelection(fallbackText);

                    new Notice('处理过程中遇到问题，已使用简化模式完成处理');
                    this.showMixedProcessingResults(plainTasks.length, fallbackResult.processedItems.length, fallbackResult.changes);
                } catch (fallbackError) {
                    console.error('ScheduleManager: 错误恢复也失败:', fallbackError);
                    new Notice('处理失败，请检查任务格式或联系开发者');
                }
            }

        } catch (error) {
            console.error('处理日程时出错:', error);
            new Notice(`处理日程失败: ${error.message}`);
        }
    }

    /**
     * 解析日程文本（支持层级结构）
     * @param text 选中的文本
     * @returns 解析结果包含日程项、普通任务项和任务组
     */
    private parseScheduleText(text: string): ParseResult {
        const lines = text.split('\n');
        const scheduleItems: ScheduleItem[] = [];
        const plainTasks: PlainTaskItem[] = [];
        let hasHierarchy = false;

        // 第一步：基础解析，识别任务类型
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];

            // 跳过空行
            if (this.isEmptyLine(line)) {
                continue;
            }

            // 检测是否包含缩进（层级结构）
            const { level, isValid } = this.detectIndentLevel(line); // 直接使用原始行，不标准化
            if (level > 0 && isValid) {
                hasHierarchy = true;
            }

            // 先尝试解析为日程项
            const scheduleItem = this.parseScheduleLine(line, i);
            if (scheduleItem) {
                scheduleItems.push(scheduleItem);
                continue;
            }

            // 再尝试解析为普通任务项
            const plainTask = this.parsePlainTaskLine(line, i);
            if (plainTask) {
                plainTasks.push(plainTask);
            }
        }

        // 第二步：如果启用层级处理且检测到层级结构，建立层级关系
        let taskGroups: TaskGroup[] = [];
        if (this.enableHierarchy && hasHierarchy) {
            // 合并所有任务进行层级分析
            const allTasks: (ScheduleItem | PlainTaskItem)[] = [...scheduleItems, ...plainTasks];

            // 按原始行索引排序，保持原有顺序
            allTasks.sort((a, b) => a.lineIndex - b.lineIndex);

            // 建立层级关系
            const tasksWithHierarchy = this.buildTaskHierarchy(allTasks);

            // 组织成任务组
            taskGroups = this.organizeTaskGroups(tasksWithHierarchy);

            // 更新原始数组中的层级信息
            this.updateOriginalArraysWithHierarchy(tasksWithHierarchy, scheduleItems, plainTasks);
        }

        return {
            scheduleItems,
            plainTasks,
            taskGroups,
            hasHierarchy
        };
    }

    /**
     * 更新原始数组中的层级信息
     * @param tasksWithHierarchy 包含层级信息的任务数组
     * @param scheduleItems 日程任务数组
     * @param plainTasks 普通任务数组
     */
    private updateOriginalArraysWithHierarchy(
        tasksWithHierarchy: (ScheduleItem | PlainTaskItem)[],
        scheduleItems: ScheduleItem[],
        plainTasks: PlainTaskItem[]
    ): void {
        for (const taskWithHierarchy of tasksWithHierarchy) {
            // 根据任务类型更新对应数组中的层级信息
            if ('startTime' in taskWithHierarchy) {
                // 日程任务
                const scheduleTask = scheduleItems.find(item =>
                    item.lineIndex === taskWithHierarchy.lineIndex
                );
                if (scheduleTask) {
                    scheduleTask.hierarchy = taskWithHierarchy.hierarchy;
                }
            } else {
                // 普通任务
                const plainTask = plainTasks.find(item =>
                    item.lineIndex === taskWithHierarchy.lineIndex
                );
                if (plainTask) {
                    plainTask.hierarchy = taskWithHierarchy.hierarchy;
                }
            }
        }
    }

    /**
     * 解析单行日程（修复版，保持原始缩进）
     * @param line 文本行
     * @param lineIndex 行索引
     * @returns 日程项或null
     */
    private parseScheduleLine(line: string, lineIndex: number): ScheduleItem | null {
        // 不要标准化缩进，直接使用原始行进行匹配
        // 匹配格式：前缀 + 时间｜持续时间｜任务名称
        // 支持的前缀：缩进 + 数字序号、短横线、复选框等
        const scheduleRegex = /^(\s*(?:\d+\.\s*)?(?:-\s*)?(?:\[[ x]\]\s*)?)((?:\d{1,2}:\d{2}(?:\s*-\s*\d{1,2}:\d{2})?)\s*｜\s*(\d+)\s*｜\s*(.+))$/;
        const match = line.match(scheduleRegex);

        if (!match) {
            return null;
        }

        const [, prefix, , durationStr, taskName] = match;
        const timeAndDuration = match[2];

        // 验证缩进格式（使用原始行）
        const { isValid } = this.detectIndentLevel(line);
        if (!isValid) {
            console.warn(`ScheduleManager: 日程任务缩进格式无效，行 ${lineIndex}: ${line}`);
            // 仍然解析，但会在后续处理中跳过层级处理
        }

        // 解析时间部分
        const timeMatch = timeAndDuration.match(/^(\d{1,2}:\d{2})(?:\s*-\s*(\d{1,2}:\d{2}))?/);
        if (!timeMatch) {
            return null;
        }

        const startTime = this.normalizeTime(timeMatch[1]);
        const endTime = timeMatch[2] ? this.normalizeTime(timeMatch[2]) : undefined;
        const duration = parseInt(durationStr);

        // 验证时间格式
        if (!this.isValidTime(startTime) || (endTime && !this.isValidTime(endTime))) {
            return null;
        }

        // 验证持续时间
        if (isNaN(duration) || duration <= 0) {
            return null;
        }

        return {
            startTime,
            endTime,
            duration,
            taskName: taskName.trim(),
            originalLine: line, // 保持原始行，包含原始缩进
            lineIndex,
            prefix: prefix || ''
        };
    }

    /**
     * 解析普通任务行（修复版，保持原始缩进）
     * @param line 文本行
     * @param lineIndex 行索引
     * @returns 普通任务项或null
     */
    private parsePlainTaskLine(line: string, lineIndex: number): PlainTaskItem | null {
        // 不要标准化缩进，直接使用原始行进行匹配
        // 匹配格式：前缀 + 任务内容（不包含时间信息）
        // 支持的前缀：缩进 + 数字序号、短横线、复选框等
        const plainTaskRegex = /^(\s*(?:\d+\.\s*)?(?:-\s*)?(?:\[[ x]\]\s*)?)((?!.*｜.*｜).+)$/;
        const match = line.match(plainTaskRegex);

        if (!match) {
            return null;
        }

        const [, prefix, taskContent] = match;

        // 排除空行和纯前缀行
        if (!taskContent.trim()) {
            return null;
        }

        // 验证缩进格式（使用原始行）
        const { isValid } = this.detectIndentLevel(line);
        if (!isValid) {
            console.warn(`ScheduleManager: 普通任务缩进格式无效，行 ${lineIndex}: ${line}`);
            // 仍然解析，但会在后续处理中跳过层级处理
        }

        return {
            taskName: taskContent.trim(),
            originalLine: line, // 保持原始行，包含原始缩进
            lineIndex,
            prefix: prefix || ''
        };
    }

    /**
     * 处理日程项数组
     * @param items 原始日程项数组
     * @returns 处理后的日程项数组和变更信息
     */
    private processScheduleItems(items: ScheduleItem[]): {
        processedItems: ScheduleItem[];
        changes: {
            timeAdjustments: number;
            durationCorrections: number;
            endTimeAdditions: number;
        };
    } {
        // 初始化变更统计
        const changes = {
            timeAdjustments: 0,
            durationCorrections: 0,
            endTimeAdditions: 0
        };

        // 第1步：排序 - 按开始时间升序排列
        const sortedItems = [...items].sort((a, b) => {
            return this.timeToMinutes(a.startTime) - this.timeToMinutes(b.startTime);
        });

        // 第2步：单次循环处理 - 自我修正 + 位置修正
        const processedItems: ScheduleItem[] = [];
        let previousEndTime = 0; // 前一个任务的结束时间（分钟）

        for (const item of sortedItems) {
            const processedItem = { ...item };

            // A. 自我修正 (Internal Normalization)
            if (!processedItem.endTime) {
                // 情况一：没有结束时间，根据开始时间和持续时间计算
                const startMinutes = this.timeToMinutes(processedItem.startTime);
                const endMinutes = startMinutes + processedItem.duration;
                processedItem.endTime = this.minutesToTime(endMinutes);
                changes.endTimeAdditions++;
            } else {
                // 情况二：有结束时间，重新计算实际持续时间
                const startMinutes = this.timeToMinutes(processedItem.startTime);
                const endMinutes = this.timeToMinutes(processedItem.endTime);
                const actualDuration = endMinutes - startMinutes;

                if (actualDuration !== processedItem.duration) {
                    processedItem.duration = actualDuration;
                    changes.durationCorrections++;
                }
            }

            // B. 位置修正 (Positional Adjustment)
            const currentStartMinutes = this.timeToMinutes(processedItem.startTime);

            if (currentStartMinutes < previousEndTime) {
                // 发生时间重叠，调整开始时间
                processedItem.startTime = this.minutesToTime(previousEndTime);
                // 重新计算结束时间
                const newEndMinutes = previousEndTime + processedItem.duration;
                processedItem.endTime = this.minutesToTime(newEndMinutes);
                previousEndTime = newEndMinutes;
                changes.timeAdjustments++;
            } else {
                // 没有重叠，更新前一个任务的结束时间
                previousEndTime = this.timeToMinutes(processedItem.endTime!);
            }

            processedItems.push(processedItem);
        }

        return { processedItems, changes };
    }

    /**
     * 处理任务组（支持层级结构的日程处理）
     * @param taskGroups 任务组数组
     * @returns 处理后的任务组和变更信息
     */
    private processTaskGroups(taskGroups: TaskGroup[]): {
        processedTaskGroups: TaskGroup[];
        changes: {
            timeAdjustments: number;
            durationCorrections: number;
            endTimeAdditions: number;
        };
    } {
        console.log(`ScheduleManager: 开始处理 ${taskGroups.length} 个任务组`);

        // 初始化变更统计
        const changes = {
            timeAdjustments: 0,
            durationCorrections: 0,
            endTimeAdditions: 0
        };

        // 分离日程任务组和普通任务组
        const scheduleTaskGroups = taskGroups.filter(group => group.type === 'schedule');
        const plainTaskGroups = taskGroups.filter(group => group.type === 'plain');

        console.log(`ScheduleManager: 日程任务组 ${scheduleTaskGroups.length} 个，普通任务组 ${plainTaskGroups.length} 个`);

        // 处理日程任务组
        const processedScheduleGroups = this.processScheduleTaskGroups(scheduleTaskGroups, changes);

        // 普通任务组不需要时间处理，直接返回
        const processedTaskGroups = [...processedScheduleGroups, ...plainTaskGroups];

        console.log(`ScheduleManager: 任务组处理完成，变更统计:`, changes);
        return { processedTaskGroups, changes };
    }

    /**
     * 处理日程任务组
     * @param scheduleTaskGroups 日程任务组数组
     * @param changes 变更统计对象
     * @returns 处理后的日程任务组数组
     */
    private processScheduleTaskGroups(
        scheduleTaskGroups: TaskGroup[],
        changes: { timeAdjustments: number; durationCorrections: number; endTimeAdditions: number }
    ): TaskGroup[] {
        // 按主任务的开始时间排序
        const sortedGroups = [...scheduleTaskGroups].sort((a, b) => {
            const aMainTask = a.mainTask as ScheduleItem;
            const bMainTask = b.mainTask as ScheduleItem;
            return this.timeToMinutes(aMainTask.startTime) - this.timeToMinutes(bMainTask.startTime);
        });

        const processedGroups: TaskGroup[] = [];
        let previousEndTime = 0; // 前一个任务组的结束时间（分钟）

        for (const group of sortedGroups) {
            const processedGroup = { ...group };
            const mainTask = processedGroup.mainTask as ScheduleItem;

            // 处理主任务的时间
            const processedMainTask = this.processScheduleTask(mainTask, previousEndTime, changes);
            processedGroup.mainTask = processedMainTask;

            // 更新前一个任务的结束时间
            previousEndTime = this.timeToMinutes(processedMainTask.endTime!);

            // 子任务不需要时间调整，但需要保持与主任务的关联
            // 这里子任务保持原样，因为它们跟随主任务

            processedGroups.push(processedGroup);

            console.log(`ScheduleManager: 处理任务组，主任务: ${processedMainTask.taskName}, 子任务数: ${processedGroup.subTasks.length}`);
        }

        return processedGroups;
    }

    /**
     * 处理单个日程任务
     * @param task 日程任务
     * @param previousEndTime 前一个任务的结束时间（分钟）
     * @param changes 变更统计对象
     * @returns 处理后的日程任务
     */
    private processScheduleTask(
        task: ScheduleItem,
        previousEndTime: number,
        changes: { timeAdjustments: number; durationCorrections: number; endTimeAdditions: number }
    ): ScheduleItem {
        const processedTask = { ...task };

        // A. 自我修正 (Internal Normalization)
        if (!processedTask.endTime) {
            // 情况一：没有结束时间，根据开始时间和持续时间计算
            const startMinutes = this.timeToMinutes(processedTask.startTime);
            const endMinutes = startMinutes + processedTask.duration;
            processedTask.endTime = this.minutesToTime(endMinutes);
            changes.endTimeAdditions++;
        } else {
            // 情况二：有结束时间，重新计算实际持续时间
            const startMinutes = this.timeToMinutes(processedTask.startTime);
            const endMinutes = this.timeToMinutes(processedTask.endTime);
            const actualDuration = endMinutes - startMinutes;

            if (actualDuration !== processedTask.duration) {
                processedTask.duration = actualDuration;
                changes.durationCorrections++;
            }
        }

        // B. 位置修正 (Positional Adjustment)
        const currentStartMinutes = this.timeToMinutes(processedTask.startTime);

        if (currentStartMinutes < previousEndTime) {
            // 发生时间重叠，调整开始时间
            processedTask.startTime = this.minutesToTime(previousEndTime);
            // 重新计算结束时间
            const newEndMinutes = previousEndTime + processedTask.duration;
            processedTask.endTime = this.minutesToTime(newEndMinutes);
            changes.timeAdjustments++;

            console.log(`ScheduleManager: 调整任务时间冲突 - ${processedTask.taskName}: ${this.minutesToTime(currentStartMinutes)} -> ${processedTask.startTime}`);
        }

        return processedTask;
    }

    /**
     * 从处理后的任务组中提取日程项
     * @param processedTaskGroups 处理后的任务组数组
     * @returns 日程项数组
     */
    private extractScheduleItemsFromGroups(processedTaskGroups: TaskGroup[]): ScheduleItem[] {
        const scheduleItems: ScheduleItem[] = [];

        for (const group of processedTaskGroups) {
            // 如果主任务是日程任务，添加到结果中
            if (group.type === 'schedule' && 'startTime' in group.mainTask) {
                scheduleItems.push(group.mainTask as ScheduleItem);
            }

            // 检查子任务中是否有日程任务
            for (const subTask of group.subTasks) {
                if ('startTime' in subTask) {
                    scheduleItems.push(subTask as ScheduleItem);
                }
            }
        }

        return scheduleItems;
    }

    /**
     * 生成处理后的日程文本
     * @param items 处理后的日程项数组
     * @returns 新的文本内容
     */
    private generateScheduleText(items: ScheduleItem[]): string {
        return items.map(item => {
            const timeRange = `${item.startTime} - ${item.endTime}`;
            return `${item.prefix}${timeRange}｜${item.duration}｜${item.taskName}`;
        }).join('\n');
    }

    /**
     * 生成混合任务文本（普通任务 + 日程任务）
     * @param plainTasks 普通任务数组
     * @param scheduleItems 处理后的日程项数组
     * @returns 新的文本内容
     */
    private generateMixedTaskText(plainTasks: PlainTaskItem[], scheduleItems: ScheduleItem[]): string {
        const result: string[] = [];

        // 先添加普通任务（保持原有顺序）
        for (const task of plainTasks) {
            result.push(`${task.prefix}${task.taskName}`);
        }

        // 再添加日程任务（已按时间排序）
        for (const item of scheduleItems) {
            const timeRange = `${item.startTime} - ${item.endTime}`;
            result.push(`${item.prefix}${timeRange}｜${item.duration}｜${item.taskName}`);
        }

        return result.join('\n');
    }

    /**
     * 生成层级结构的任务文本（修复版 - 集成排序功能）
     * @param taskGroups 任务组数组
     * @param processedScheduleItems 处理后的日程项数组
     * @returns 新的文本内容
     */
    private generateHierarchicalTaskText(taskGroups: TaskGroup[], processedScheduleItems: ScheduleItem[]): string {
        console.log(`ScheduleManager: 开始生成层级结构文本，任务组数量: ${taskGroups.length}`);

        const result: string[] = [];

        // 第一步：对任务组进行排序（恢复排序功能）
        const sortedTaskGroups = this.sortTaskGroups(taskGroups);

        // 第二步：更新任务序号
        const numberedTaskGroups = this.updateTaskNumbers(sortedTaskGroups);

        // 第三步：生成文本
        for (const taskGroup of numberedTaskGroups) {
            console.log(`ScheduleManager: 处理任务组，类型: ${taskGroup.type}, 主任务: ${taskGroup.mainTask.taskName}, 子任务数: ${taskGroup.subTasks.length}`);

            // 处理主任务
            const mainTaskText = this.generateTaskText(taskGroup.mainTask, processedScheduleItems);
            if (mainTaskText) {
                result.push(mainTaskText);
                console.log(`ScheduleManager: 添加主任务文本: ${mainTaskText}`);
            }

            // 处理子任务（按层级顺序排序，保持原有的层级缩进）
            const sortedSubTasks = this.sortSubTasksByHierarchy(taskGroup.subTasks);
            for (const subTask of sortedSubTasks) {
                const subTaskText = this.generateTaskText(subTask, processedScheduleItems);
                if (subTaskText) {
                    result.push(subTaskText);
                    console.log(`ScheduleManager: 添加子任务文本: ${subTaskText}`);
                }
            }
        }

        const finalText = result.join('\n');
        console.log(`ScheduleManager: 层级结构文本生成完成，总行数: ${result.length}`);
        console.log(`ScheduleManager: 排序功能已应用 - 有时间任务按时间排序，无时间任务按字母排序`);
        return finalText;
    }

    /**
     * 按层级顺序排序子任务
     * @param subTasks 子任务数组
     * @returns 排序后的子任务数组
     */
    private sortSubTasksByHierarchy(subTasks: (ScheduleItem | PlainTaskItem)[]): (ScheduleItem | PlainTaskItem)[] {
        return [...subTasks].sort((a, b) => {
            // 首先按层级排序
            const aLevel = a.hierarchy?.level || 0;
            const bLevel = b.hierarchy?.level || 0;
            if (aLevel !== bLevel) {
                return aLevel - bLevel;
            }

            // 同层级按原始行索引排序
            return a.lineIndex - b.lineIndex;
        });
    }

    /**
     * 生成单个任务的文本（增强版，支持层级结构）
     * @param task 任务项（可以是日程任务或普通任务）
     * @param processedScheduleItems 处理后的日程项数组
     * @returns 任务文本
     */
    private generateTaskText(task: ScheduleItem | PlainTaskItem, processedScheduleItems: ScheduleItem[]): string {
        const prefix = this.reconstructPrefix(task);

        // 如果是日程任务，需要使用处理后的时间信息
        if ('startTime' in task) {
            // 查找对应的处理后的日程项
            const processedItem = processedScheduleItems.find(item =>
                item.lineIndex === task.lineIndex
            );

            if (processedItem) {
                // 使用处理后的时间信息
                const timeRange = `${processedItem.startTime} - ${processedItem.endTime}`;
                const taskText = `${prefix}${timeRange}｜${processedItem.duration}｜${processedItem.taskName}`;
                console.log(`ScheduleManager: 生成日程任务文本 (已处理): ${taskText}`);
                return taskText;
            } else {
                // 如果没有找到处理后的项，使用原始信息
                const timeRange = task.endTime ? `${task.startTime} - ${task.endTime}` : task.startTime;
                const taskText = `${prefix}${timeRange}｜${task.duration}｜${task.taskName}`;
                console.log(`ScheduleManager: 生成日程任务文本 (原始): ${taskText}`);
                return taskText;
            }
        } else {
            // 普通任务
            const taskText = `${prefix}${task.taskName}`;
            console.log(`ScheduleManager: 生成普通任务文本: ${taskText}`);
            return taskText;
        }
    }

    /**
     * 重构任务前缀（修复版 - 保持绝对缩进位置）
     * @param task 任务项
     * @returns 重构后的前缀
     */
    private reconstructPrefix(task: ScheduleItem | PlainTaskItem): string {
        if (!task.hierarchy) {
            // 没有层级信息，直接返回原始前缀
            console.log(`ScheduleManager: 无层级信息，使用原始前缀: "${task.prefix}"`);
            return task.prefix;
        }

        // 使用原始缩进字符串保持绝对位置
        const originalIndent = task.hierarchy.absoluteIndent;

        // 提取原始前缀中的非缩进部分（如数字序号、短横线、复选框等）
        const trimmedPrefix = task.prefix.trim();

        // 重构前缀：原始缩进 + 非缩进部分 + 空格（如果有非缩进部分）
        const reconstructedPrefix = originalIndent + trimmedPrefix + (trimmedPrefix ? ' ' : '');

        console.log(`ScheduleManager: 重构前缀 - 原始: "${task.prefix}", 层级: ${task.hierarchy.level}, 绝对缩进: "${originalIndent}", 重构: "${reconstructedPrefix}"`);

        return reconstructedPrefix;
    }

    /**
     * 生成任务组的文本（新增方法，用于更精确的任务组文本生成）
     * @param taskGroup 任务组
     * @param processedScheduleItems 处理后的日程项数组
     * @returns 任务组文本数组
     */
    private generateTaskGroupText(taskGroup: TaskGroup, processedScheduleItems: ScheduleItem[]): string[] {
        const result: string[] = [];

        // 生成主任务文本
        const mainTaskText = this.generateTaskText(taskGroup.mainTask, processedScheduleItems);
        if (mainTaskText) {
            result.push(mainTaskText);
        }

        // 生成子任务文本（按层级和原始顺序排序）
        const sortedSubTasks = this.sortSubTasksByHierarchy(taskGroup.subTasks);
        for (const subTask of sortedSubTasks) {
            const subTaskText = this.generateTaskText(subTask, processedScheduleItems);
            if (subTaskText) {
                result.push(subTaskText);
            }
        }

        return result;
    }

    /**
     * 验证输出格式的正确性
     * @param taskGroups 任务组数组
     * @param generatedText 生成的文本
     * @returns 验证结果和问题描述
     */
    private validateOutputFormat(taskGroups: TaskGroup[], generatedText: string): {
        isValid: boolean;
        issues: string[];
    } {
        const issues: string[] = [];
        const lines = generatedText.split('\n');

        // 验证行数是否匹配
        const expectedLineCount = taskGroups.reduce((count, group) => {
            return count + 1 + group.subTasks.length; // 主任务 + 子任务数量
        }, 0);

        if (lines.length !== expectedLineCount) {
            issues.push(`行数不匹配：期望 ${expectedLineCount} 行，实际 ${lines.length} 行`);
        }

        // 验证缩进格式
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const { level, isValid } = this.detectIndentLevel(line);

            if (!isValid) {
                issues.push(`第 ${i + 1} 行缩进格式无效: ${line}`);
            }
        }

        return {
            isValid: issues.length === 0,
            issues
        };
    }

    /**
     * 优化层级结构文本生成（备用方法，用于处理复杂情况）
     * @param taskGroups 任务组数组
     * @param processedScheduleItems 处理后的日程项数组
     * @returns 优化后的文本内容
     */
    private generateOptimizedHierarchicalText(taskGroups: TaskGroup[], processedScheduleItems: ScheduleItem[]): string {
        const result: string[] = [];

        // 按原始顺序处理任务组
        const sortedTaskGroups = [...taskGroups].sort((a, b) => a.originalOrder - b.originalOrder);

        for (const taskGroup of sortedTaskGroups) {
            // 使用专门的任务组文本生成方法
            const groupTexts = this.generateTaskGroupText(taskGroup, processedScheduleItems);
            result.push(...groupTexts);
        }

        const finalText = result.join('\n');

        // 验证输出格式
        const validation = this.validateOutputFormat(taskGroups, finalText);
        if (!validation.isValid) {
            console.warn('ScheduleManager: 输出格式验证失败:', validation.issues);
            // 可以选择回退到简单格式或者尝试修复
        }

        return finalText;
    }

    /**
     * 对任务组进行排序（恢复排序功能）
     * @param taskGroups 任务组数组
     * @returns 排序后的任务组数组
     */
    private sortTaskGroups(taskGroups: TaskGroup[]): TaskGroup[] {
        console.log(`ScheduleManager: 开始对 ${taskGroups.length} 个任务组进行排序`);

        // 分离有时间和无时间的任务组
        const scheduleTaskGroups: TaskGroup[] = [];
        const plainTaskGroups: TaskGroup[] = [];

        for (const group of taskGroups) {
            if (group.type === 'schedule') {
                scheduleTaskGroups.push(group);
            } else {
                plainTaskGroups.push(group);
            }
        }

        console.log(`ScheduleManager: 有时间任务组 ${scheduleTaskGroups.length} 个，无时间任务组 ${plainTaskGroups.length} 个`);

        // 对有时间的任务组按开始时间排序（升序：早→晚）
        const sortedScheduleGroups = scheduleTaskGroups.sort((a, b) => {
            const aMainTask = a.mainTask as ScheduleItem;
            const bMainTask = b.mainTask as ScheduleItem;
            const aTime = this.timeToMinutes(aMainTask.startTime);
            const bTime = this.timeToMinutes(bMainTask.startTime);

            console.log(`ScheduleManager: 比较时间 ${aMainTask.taskName}(${aMainTask.startTime}) vs ${bMainTask.taskName}(${bMainTask.startTime})`);
            return aTime - bTime;
        });

        // 对无时间的任务组按首字母排序（升序：A→Z）
        const sortedPlainGroups = plainTaskGroups.sort((a, b) => {
            const aName = a.mainTask.taskName.toLowerCase();
            const bName = b.mainTask.taskName.toLowerCase();

            console.log(`ScheduleManager: 比较字母 ${aName} vs ${bName}`);
            return aName.localeCompare(bName);
        });

        // 合并结果：有时间的任务在前，无时间的任务在后
        const sortedTaskGroups = [...sortedScheduleGroups, ...sortedPlainGroups];

        console.log(`ScheduleManager: 排序完成，最终顺序:`);
        sortedTaskGroups.forEach((group, index) => {
            console.log(`  ${index + 1}. ${group.mainTask.taskName} (${group.type})`);
        });

        return sortedTaskGroups;
    }

    /**
     * 更新任务序号
     * @param taskGroups 排序后的任务组数组
     * @returns 更新序号后的任务组数组
     */
    private updateTaskNumbers(taskGroups: TaskGroup[]): TaskGroup[] {
        console.log(`ScheduleManager: 开始更新任务序号`);

        const updatedTaskGroups = taskGroups.map((group, index) => {
            const updatedGroup = { ...group };
            const newNumber = index + 1;

            // 更新主任务的序号
            const mainTask = { ...updatedGroup.mainTask };

            // 更新前缀中的序号
            const updatedPrefix = this.updatePrefixNumber(mainTask.prefix, newNumber);
            mainTask.prefix = updatedPrefix;

            // 如果有层级信息，也需要更新
            if (mainTask.hierarchy) {
                const updatedHierarchy = { ...mainTask.hierarchy };
                // 重新构建前缀以反映新的序号
                const trimmedPrefix = updatedPrefix.trim();
                updatedHierarchy.absoluteIndent = mainTask.hierarchy.absoluteIndent;
                mainTask.hierarchy = updatedHierarchy;
            }

            updatedGroup.mainTask = mainTask;

            console.log(`ScheduleManager: 更新任务序号 ${group.mainTask.taskName}: ${this.extractNumberFromPrefix(group.mainTask.prefix)} → ${newNumber}`);

            return updatedGroup;
        });

        return updatedTaskGroups;
    }

    /**
     * 更新前缀中的序号
     * @param prefix 原始前缀
     * @param newNumber 新序号
     * @returns 更新后的前缀
     */
    private updatePrefixNumber(prefix: string, newNumber: number): string {
        // 匹配前缀中的数字序号模式
        const numberPattern = /^(\s*)(\d+)(\.\s*)(.*)/;
        const match = prefix.match(numberPattern);

        if (match) {
            const [, indent, , dotSpace, rest] = match;
            return `${indent}${newNumber}${dotSpace}${rest}`;
        }

        // 如果没有匹配到数字模式，返回原前缀
        return prefix;
    }

    /**
     * 从前缀中提取序号
     * @param prefix 前缀字符串
     * @returns 序号，如果没有找到则返回0
     */
    private extractNumberFromPrefix(prefix: string): number {
        const numberPattern = /(\d+)/;
        const match = prefix.match(numberPattern);
        return match ? parseInt(match[1]) : 0;
    }

    /**
     * 将时间字符串转换为分钟数
     * @param timeStr 时间字符串 (HH:MM)
     * @returns 分钟数
     */
    private timeToMinutes(timeStr: string): number {
        const [hours, minutes] = timeStr.split(':').map(Number);
        return hours * 60 + minutes;
    }

    /**
     * 将分钟数转换为时间字符串
     * @param minutes 分钟数
     * @returns 时间字符串 (HH:MM)
     */
    private minutesToTime(minutes: number): string {
        // 处理跨天情况
        const totalMinutes = minutes % (24 * 60);
        const hours = Math.floor(totalMinutes / 60);
        const mins = totalMinutes % 60;
        return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
    }

    /**
     * 标准化时间格式
     * @param timeStr 时间字符串
     * @returns 标准化的时间字符串 (HH:MM)
     */
    private normalizeTime(timeStr: string): string {
        const [hours, minutes] = timeStr.split(':').map(Number);
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    }

    /**
     * 验证时间格式是否有效
     * @param timeStr 时间字符串
     * @returns 是否有效
     */
    private isValidTime(timeStr: string): boolean {
        const timeRegex = /^([01]?\d|2[0-3]):([0-5]?\d)$/;
        const match = timeStr.match(timeRegex);

        if (!match) {
            return false;
        }

        const hours = parseInt(match[1]);
        const minutes = parseInt(match[2]);

        return hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59;
    }

    /**
     * 显示处理结果
     * @param totalItems 总处理项数
     * @param changes 变更统计
     */
    private showProcessingResults(totalItems: number, changes: {
        timeAdjustments: number;
        durationCorrections: number;
        endTimeAdditions: number;
    }): void {
        const messages: string[] = [`✅ 已处理 ${totalItems} 个日程项`];

        if (changes.endTimeAdditions > 0) {
            messages.push(`📅 自动添加了 ${changes.endTimeAdditions} 个结束时间`);
        }

        if (changes.durationCorrections > 0) {
            messages.push(`⏱️ 修正了 ${changes.durationCorrections} 个持续时间`);
        }

        if (changes.timeAdjustments > 0) {
            messages.push(`🔄 调整了 ${changes.timeAdjustments} 个时间冲突`);
        }

        // 显示主要消息
        new Notice(messages[0]);

        // 如果有详细变更，显示详细信息
        if (messages.length > 1) {
            setTimeout(() => {
                new Notice(messages.slice(1).join('，'), 4000);
            }, 1000);
        }
    }

    /**
     * 显示混合处理结果
     * @param plainTaskCount 普通任务数量
     * @param scheduleItemCount 日程项数量
     * @param changes 变更统计
     */
    private showMixedProcessingResults(
        plainTaskCount: number,
        scheduleItemCount: number,
        changes: {
            timeAdjustments: number;
            durationCorrections: number;
            endTimeAdditions: number;
        }
    ): void {
        const totalItems = plainTaskCount + scheduleItemCount;
        const messages: string[] = [];

        if (plainTaskCount > 0 && scheduleItemCount > 0) {
            messages.push(`✅ 已处理 ${totalItems} 个任务（${plainTaskCount} 个普通任务，${scheduleItemCount} 个日程任务）`);
        } else if (plainTaskCount > 0) {
            messages.push(`✅ 已处理 ${plainTaskCount} 个普通任务`);
        } else {
            messages.push(`✅ 已处理 ${scheduleItemCount} 个日程任务`);
        }

        if (changes.endTimeAdditions > 0) {
            messages.push(`📅 自动添加了 ${changes.endTimeAdditions} 个结束时间`);
        }

        if (changes.durationCorrections > 0) {
            messages.push(`⏱️ 修正了 ${changes.durationCorrections} 个持续时间`);
        }

        if (changes.timeAdjustments > 0) {
            messages.push(`🔄 调整了 ${changes.timeAdjustments} 个时间冲突`);
        }

        // 显示主要消息
        new Notice(messages[0]);

        // 如果有详细变更，显示详细信息
        if (messages.length > 1) {
            setTimeout(() => {
                new Notice(messages.slice(1).join('，'), 4000);
            }, 1000);
        }
    }
}

/**
 * 处理选中日程的主要函数
 * @param options 选项
 */
export async function handleScheduleProcessing(options: ScheduleManagerOptions): Promise<void> {
    const manager = new ScheduleManager(options);
    await manager.processSelectedSchedule();
}
