import { Editor, Notice, App, TFile } from 'obsidian';

// 类型定义
interface NoteReferenceReverserOptions {
	editor: Editor;
	app: App;
}

// 主要功能函数
export async function reverseNoteReferences(options: NoteReferenceReverserOptions): Promise<void> {
	const { editor, app } = options;
	
	try {
		// 获取选中的内容
		const selectedText = editor.getSelection().trim();
		if (!selectedText) {
			new Notice('请先选择需要反转的笔记引用内容');
			return;
		}

		// 按行拆分内容
		const lines = selectedText.split('\n').filter(line => line.trim());
		const reversedLines: string[] = [];

		for (const line of lines) {
			// 匹配格式：文本内容。[[笔记链接]]
			const match = line.match(/(.*?)\[\[([^\]]+)\]\]/);
			if (!match) continue;

			const [_, summary, noteTitle] = match;
			if (!summary || !noteTitle) continue;

			// 在根目录下查找笔记文件
			const files = app.vault.getMarkdownFiles();
			const noteFile = files.find(file => file.basename === noteTitle);

			if (!noteFile) {
				console.warn(`未找到笔记: ${noteTitle}`);
				// 如果找不到笔记文件，使用原有的摘要
				const reversedReference = `[[${noteTitle}]]｜${summary.trim()}`;
				reversedLines.push(reversedReference);
				continue;
			}

			try {
				// 读取笔记内容
				const content = await app.vault.read(noteFile);
				
				// 提取摘要
				const summaryMatch = content.match(/摘要：(.*?)\n/);
				const noteSummary = summaryMatch ? summaryMatch[1].trim() : summary.trim();

				// 构建新的引用格式
				const reversedReference = `[[${noteTitle}]]｜${noteSummary}`;
				reversedLines.push(reversedReference);
			} catch (error) {
				console.error(`处理笔记 ${noteTitle} 时出错:`, error);
				// 如果处理出错，使用原有的摘要
				const reversedReference = `[[${noteTitle}]]｜${summary.trim()}`;
				reversedLines.push(reversedReference);
			}
		}

		// 如果有成功处理的行，替换选中内容
		if (reversedLines.length > 0) {
			editor.replaceSelection(reversedLines.join('\n'));
			new Notice(`✨ 已反转 ${reversedLines.length} 个笔记引用！`);
		} else {
			new Notice('没有找到可以反转的笔记引用');
		}
	} catch (error) {
		console.error('反转笔记引用时出错:', error);
		new Notice('反转笔记引用失败: ' + error.message);
	}
}
