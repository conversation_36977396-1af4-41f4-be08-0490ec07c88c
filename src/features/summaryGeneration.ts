import { Editor, Notice, TFile, App } from 'obsidian';

// 类型定义
interface SummaryGenerationSettings {
	apiEndpoint: string;
	apiKey: string;
}

interface SummaryGenerationOptions {
	editor: Editor;
	file: TFile;
	app: App;
	settings: SummaryGenerationSettings;
}

interface SummaryResponse {
	title: string;
	summary: string;
	'key-points': string[];
	'action-items': string[];
}

// 提示词
export const SUMMARY_PROMPT = `Extract key information from the provided text, including a concise title, a one-sentence summary, and a list of key points, and a list of action items, as a detailed summary, and output the results in a JSON format. The key points should be no more than 5 items.

Ensure that all responses are generated in Chinese and that the JSON structure strictly follows the format specified using English half-width characters.

# Steps

1. Title Generation: Create a concise and clear title for the notes.
2. Detail Summary: Write a detailed summary of the notes, helping users decide whether to engage with the content.The summary should be concise but informative (50-200 words), should capture the key points and main ideas of the notes, Ensure summaries are detailed enough to serve as good search targets
3. Key Point Summary: Identify the core arguments and key information in the text. Create a concise summary by listing up to 5 key points.
4. Action Item Summary: Extract suggested actions from the text and list them concisely.
5. JSON Output: Structure the extracted information into a JSON object with the fields "title," "summary," "key-points", and "action-items".

# Output Format

The output should be a JSON object with the following structure:

{
"title": "Concise Note Title",
"summary": "Detail Summary.",
"key-points": ["Key point 1", "Key point 2", "Key point 3", ...],
"action-items": ["Action item 1", "Action item 2", ...]
}

The title field should be a string containing the concise note title. The summary field should be Ensure detailed enough to serve as good search targets. The key-points field should be an array of strings, each string being a key point extracted from the text. The action-items field should be an array of strings, each string being an action item extracted from the text. All fields should be strings except key-points and action-items, which are arrays of strings.

Important: Return ONLY ONE summary object, not an array of summaries.`;

// API调用函数
async function callSummaryAPI(
	content: string,
	settings: SummaryGenerationSettings
): Promise<SummaryResponse | null> {
	try {
		if (!content) {
			throw new Error('内容不能为空');
		}

		if (!settings.apiEndpoint || !settings.apiKey) {
			throw new Error('API配置不完整，请检查设置');
		}

		const requestBody = {
			contents: [{
				parts: [
					{ text: SUMMARY_PROMPT + "\n\nText to analyze:\n" + content }
				]
			}],
			generationConfig: {
				temperature: 0.7,
				topK: 40,
				topP: 0.95,
				maxOutputTokens: 8192,
				response_mime_type: "application/json"
			}
		};

		console.log('摘要 API 请求体:', JSON.stringify(requestBody, null, 2));

		const response = await fetch(`${settings.apiEndpoint}?key=${settings.apiKey}`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify(requestBody)
		});

		if (!response.ok) {
			throw new Error(`API 请求失败: ${response.statusText}`);
		}

		const data = await response.json();
		console.log('摘要 API 原始响应:', JSON.stringify(data, null, 2));

		if (!data.candidates?.[0]?.content?.parts?.[0]?.text) {
			throw new Error('API 响应格式不正确');
		}

		const responseText = data.candidates[0].content.parts[0].text;
		console.log('API 响应文本:', responseText);

		try {
			let parsedResponse = JSON.parse(responseText);
			
			// 如果返回的是数组，只取第一个结果
			if (Array.isArray(parsedResponse)) {
				console.log('API 返回了多个结果，只使用第一个');
				parsedResponse = parsedResponse[0];
			}
			
			console.log('解析后的响应:', JSON.stringify(parsedResponse, null, 2));

			// 验证响应格式
			if (!parsedResponse.title || typeof parsedResponse.title !== 'string') {
				console.error('响应缺少标题字段或格式不正确:', parsedResponse);
				throw new Error('API 响应缺少标题字段');
			}

			if (!parsedResponse.summary || typeof parsedResponse.summary !== 'string') {
				console.error('响应缺少摘要字段或格式不正确:', parsedResponse);
				throw new Error('API 响应缺少摘要字段');
			}

			if (!Array.isArray(parsedResponse['key-points'])) {
				console.error('响应缺少要点数组或格式不正确:', parsedResponse);
				throw new Error('API 响应缺少要点数组');
			}

			if (!Array.isArray(parsedResponse['action-items'])) {
				console.error('响应缺少行动项数组或格式不正确:', parsedResponse);
				throw new Error('API 响应缺少行动项数组');
			}

			return parsedResponse;
		} catch (parseError) {
			console.error('解析 JSON 响应失败:', parseError);
			console.error('原始响应文本:', responseText);
			throw new Error('解析 API 响应失败: ' + parseError.message);
		}
	} catch (error) {
		console.error('摘要 API 调用失败:', error);
		new Notice(`摘要 API 调用失败: ${error.message}`);
		return null;
	}
}

// 文件名更新函数
async function updateFileName(
	file: TFile, 
	newTitle: string, 
	app: App
): Promise<string | null> {
	try {
		// 添加参数验证
		if (!file || !newTitle) {
			console.error('文件或新标题为空:', { file, newTitle });
			throw new Error('文件或新标题参数无效');
		}

		const currentName = file.basename;
		console.log('当前文件名:', currentName);

		// 支持12位或14位时间戳
		const timestamp = currentName.match(/^\d{12,14}/)?.[0];
		console.log('提取的时间戳:', timestamp);
		
		if (!timestamp) {
			throw new Error('文件名不符合时间戳格式（需要12-14位数字）');
		}

		// 处理标题中的特殊字符，将其替换为下划线，但保留竖线
		const safeTitle = newTitle.replace(/[\/\\:*?"<>]/g, '_');
		console.log('处理后的安全标题:', safeTitle);
		
		const newName = `${timestamp}｜${safeTitle}`;
		console.log('新文件名:', newName);

		// 验证文件路径
		if (!file.path) {
			throw new Error('文件路径无效');
		}

		const newPath = file.path.replace(file.basename, newName);
		console.log('新文件路径:', newPath);
		
		await app.fileManager.renameFile(file, newPath);
		return newName;
	} catch (error) {
		console.error('更新文件名失败:', error);
		new Notice(`更新文件名失败: ${error.message}`);
		return null;
	}
}

// 内容生成函数
function generateNewContent(originalContent: string, response: SummaryResponse): string {
	const sections = [
		`摘要：${response.summary}\n`,
		'> [!summary] 要点',
		...response['key-points'].map((point, index) => `> ${index + 1}. ${point}`),
		'',
		'> [!attention] 怎么做？',
		...response['action-items'].map((item, index) => `> ${index + 1}. ${item}`),
		'',
		'---',
		'',
		originalContent
	];

	return sections.join('\n');
}

// 主要功能函数
export async function updateNoteSummary(options: SummaryGenerationOptions): Promise<void> {
	const { editor, file, app, settings } = options;
	
	try {
		new Notice('开始生成摘要...');
		const content = editor.getValue();
		
		// 调用 AI API
		new Notice('正在调用 AI API...');
		const response = await callSummaryAPI(content, settings);
		if (!response) {
			new Notice('无法获取 AI 响应');
			return;
		}
		new Notice('成功获取 AI 响应');

		// 更新文件名
		new Notice('正在更新文件名...');
		const newFileName = await updateFileName(file, response.title, app);
		if (!newFileName) {
			new Notice('更新文件名失败');
			return;
		}
		new Notice('文件名更新成功');

		// 生成新的笔记内容
		new Notice('正在生成新的笔记内容...');
		const newContent = generateNewContent(content, response);
		
		// 更新笔记内容
		await app.vault.modify(file, newContent);
		
		new Notice('✨ 摘要更新完成！');
	} catch (error) {
		console.error('更新摘要时出错:', error);
		new Notice('更新摘要时出错: ' + error.message);
	}
}
