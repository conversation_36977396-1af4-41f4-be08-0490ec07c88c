import { App, PluginSettingTab, Setting } from 'obsidian';
import { MyPluginSettings } from './types';

// 插件默认设置
export const DEFAULT_SETTINGS: MyPluginSettings = {
	apiKey: 'AIzaSyAbkSsHNSQi_bfKowJTYHXMa7xdqVQlWl4',
	apiEndpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent',
	archivePath: '/03-仓库/01-笔记/02-存档',
	vocabularyPath: '/01-构建/04-Archives/02-Areas/01-单词',
	taskLogPath: '/01-构建/04-Archives/01-Projects/02-提醒事项完成日志.md',
	thoughtsPath: '/01-构建/04-Archives/01-Projects/03-想法瀑布流.md',
	obsidianRoot: '/Users/<USER>/Library/Mobile Documents/iCloud~md~obsidian/Documents/ForeverNotes',
	conceptPath: '/01-构建/04-Archives/02-Areas/03-词条',
	// 任务管理默认设置
	taskScanFiles: '01-构建/01-Projects/01-日记.md',
	taskPanelPath: '01-构建/01-Projects/03 - 任务面板.md'
};

// 设置选项卡类
export class SummarySettingTab extends PluginSettingTab {
	plugin: {
		settings: MyPluginSettings;
		saveSettings(): Promise<void>;
	};

	constructor(app: App, plugin: any) {
		super(app, plugin);
		this.plugin = plugin;
	}

	display(): void {
		const { containerEl } = this;
		containerEl.empty();
		containerEl.createEl('h2', { text: '摘要插件设置' });

		// API Key 设置
		new Setting(containerEl)
			.setName('API Key')
			.setDesc('用于调用 AI 服务的 API 密钥')
			.addText(text => text
				.setPlaceholder('输入 API Key')
				.setValue(this.plugin.settings.apiKey)
				.onChange(async (value) => {
					this.plugin.settings.apiKey = value;
					await this.plugin.saveSettings();
				}));

		// API 端点设置
		new Setting(containerEl)
			.setName('API 端点')
			.setDesc('AI 服务的 API 端点地址')
			.addText(text => text
				.setPlaceholder('输入 API 端点')
				.setValue(this.plugin.settings.apiEndpoint)
				.onChange(async (value) => {
					this.plugin.settings.apiEndpoint = value;
					await this.plugin.saveSettings();
				}));

		// 存档路径设置
		new Setting(containerEl)
			.setName('存档路径')
			.setDesc('笔记存档的目录路径')
			.addText(text => text
				.setPlaceholder('输入存档路径')
				.setValue(this.plugin.settings.archivePath)
				.onChange(async (value) => {
					this.plugin.settings.archivePath = value;
					await this.plugin.saveSettings();
				}));

		// 词汇路径设置
		new Setting(containerEl)
			.setName('词汇路径')
			.setDesc('单词学习文件的目录路径')
			.addText(text => text
				.setPlaceholder('输入词汇路径')
				.setValue(this.plugin.settings.vocabularyPath)
				.onChange(async (value) => {
					this.plugin.settings.vocabularyPath = value;
					await this.plugin.saveSettings();
				}));

		// 任务日志路径设置
		new Setting(containerEl)
			.setName('任务日志路径')
			.setDesc('任务完成日志文件的路径')
			.addText(text => text
				.setPlaceholder('输入任务日志路径')
				.setValue(this.plugin.settings.taskLogPath)
				.onChange(async (value) => {
					this.plugin.settings.taskLogPath = value;
					await this.plugin.saveSettings();
				}));

		// 想法路径设置
		new Setting(containerEl)
			.setName('想法路径')
			.setDesc('想法瀑布流文件的路径')
			.addText(text => text
				.setPlaceholder('输入想法路径')
				.setValue(this.plugin.settings.thoughtsPath)
				.onChange(async (value) => {
					this.plugin.settings.thoughtsPath = value;
					await this.plugin.saveSettings();
				}));

		// Obsidian 根目录设置
		new Setting(containerEl)
			.setName('Obsidian 根目录')
			.setDesc('Obsidian 笔记库的根目录路径')
			.addText(text => text
				.setPlaceholder('输入根目录路径')
				.setValue(this.plugin.settings.obsidianRoot)
				.onChange(async (value) => {
					this.plugin.settings.obsidianRoot = value;
					await this.plugin.saveSettings();
				}));

		// 概念路径设置
		new Setting(containerEl)
			.setName('概念路径')
			.setDesc('概念词条文件的目录路径')
			.addText(text => text
				.setPlaceholder('输入概念路径')
				.setValue(this.plugin.settings.conceptPath)
				.onChange(async (value) => {
					this.plugin.settings.conceptPath = value;
					await this.plugin.saveSettings();
				}));

		// 任务管理设置分组
		containerEl.createEl('h3', { text: '任务管理设置' });

		// 任务扫描文件设置
		new Setting(containerEl)
			.setName('任务扫描文件')
			.setDesc('需要扫描任务的笔记文件路径，多个文件用 | 分隔')
			.addTextArea(text => text
				.setPlaceholder('例如：01-构建/01-Projects/01-日记.md|02-工作/任务清单.md')
				.setValue(this.plugin.settings.taskScanFiles)
				.onChange(async (value) => {
					this.plugin.settings.taskScanFiles = value;
					await this.plugin.saveSettings();
				}));

		// 任务面板路径设置
		new Setting(containerEl)
			.setName('任务面板路径')
			.setDesc('任务面板文件的路径')
			.addText(text => text
				.setPlaceholder('输入任务面板文件路径')
				.setValue(this.plugin.settings.taskPanelPath)
				.onChange(async (value) => {
					this.plugin.settings.taskPanelPath = value;
					await this.plugin.saveSettings();
				}));
	}
} 
